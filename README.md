# 营养追踪器 (Nutrition Tracker)

一个基于 Vue 3 + TypeScript + Naive UI 的营养记录和管理应用。

## 功能特性

### 核心功能
- 🍎 **食物库管理**: 创建和管理个人食物库，支持营养成分录入
- 📊 **营养记录**: 记录每日各餐次的食物摄入量
- 📈 **营养统计**: 自动计算每日营养摄入总量和分析
- 🏷️ **标签系统**: 支持食物分类标签，包括系统预设和自定义标签
- 👤 **个人资料**: 管理个人信息，计算BMI和基础代谢率

### 技术特性
- 🎨 **现代化UI**: 基于 Naive UI 组件库，界面美观易用
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🔄 **状态管理**: 使用 Pinia 进行状态管理
- 🛣️ **路由管理**: Vue Router 4 单页应用路由
- 📝 **TypeScript**: 完整的类型支持
- 🗄️ **数据库设计**: 完整的 MySQL 数据库结构设计

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **语言**: TypeScript
- **UI组件库**: Naive UI
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **图标**: @vicons/ionicons5
- **日期处理**: Day.js
- **HTTP客户端**: Axios
- **数据库**: MySQL

## 项目结构

```
vite-nutrition/
├── src/
│   ├── components/          # 公共组件
│   │   └── AppLayout.vue   # 应用布局组件
│   ├── views/              # 页面组件
│   │   ├── Dashboard.vue   # 仪表盘
│   │   ├── Foods.vue       # 食物库
│   │   ├── AddFood.vue     # 添加食物
│   │   ├── EditFood.vue    # 编辑食物
│   │   ├── Records.vue     # 营养记录
│   │   ├── AddRecord.vue   # 添加记录
│   │   ├── Tags.vue        # 标签管理
│   │   └── Profile.vue     # 个人资料
│   ├── stores/             # 状态管理
│   │   ├── user.ts         # 用户状态
│   │   ├── food.ts         # 食物状态
│   │   └── nutrition.ts    # 营养记录状态
│   ├── router/             # 路由配置
│   │   └── index.ts
│   └── main.ts             # 应用入口
├── database/               # 数据库设计
│   └── schema.sql          # 数据库结构
├── docs/                   # 文档
│   └── api-design.md       # API接口设计
└── README.md
```

## 数据库设计

### 主要数据表
1. **users** - 用户表
2. **food_tags** - 食物标签表
3. **foods** - 食物表
4. **food_tag_relations** - 食物标签关联表
5. **daily_nutrition_records** - 每日营养摄入记录表
6. **daily_nutrition_summary** - 每日营养汇总表

### 特色设计
- 支持扩展营养成分的 JSON 存储
- 系统标签和用户自定义标签分离
- 自动触发器维护每日营养汇总
- 完整的索引优化

## 快速开始

### 环境要求
- Node.js >= 16
- pnpm (推荐) 或 npm
- MySQL >= 8.0

### 安装依赖
```bash
pnpm install
```

### 开发环境运行
```bash
pnpm dev
```

### 构建生产版本
```bash
pnpm build
```

### 数据库初始化
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE nutrition_tracker CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入数据库结构
mysql -u root -p nutrition_tracker < database/schema.sql
```

## 页面预览

### 1. 仪表盘
- 日期选择器
- 营养摄入概览卡片（热量、蛋白质、碳水、脂肪）
- 各餐次记录展示
- 快速添加食物入口

### 2. 添加食物页面
基于提供的设计图实现，包含：
- 食物基本信息（名称、品牌、条形码）
- 详细营养成分输入（每100g）
- 维生素和矿物质百分比
- 标签选择
- 扩展营养成分支持

### 3. 食物库
- 搜索和筛选功能
- 标签筛选
- 公共/私有食物切换
- 表格展示食物信息
- 编辑/删除操作

### 4. 营养记录
- 日期范围筛选
- 餐次筛选
- 记录列表展示
- 快速添加记录

### 5. 标签管理
- 系统标签展示
- 自定义标签管理
- 颜色选择器
- 标签描述

### 6. 个人资料
- 基本信息管理
- BMI 自动计算
- 基础代谢率计算
- 健康指标展示

## 开发计划

### 已完成
- ✅ 项目基础架构搭建
- ✅ 数据库结构设计
- ✅ 状态管理设计
- ✅ 路由配置
- ✅ 主要页面组件开发
- ✅ API 接口设计文档

### 待开发
- ⏳ 后端 API 实现
- ⏳ 用户认证系统
- ⏳ 数据持久化
- ⏳ 图表统计功能
- ⏳ 移动端适配优化
- ⏳ 数据导入导出
- ⏳ 营养建议功能

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
