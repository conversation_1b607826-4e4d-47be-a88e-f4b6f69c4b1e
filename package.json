{"name": "vite-nutrition", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@vicons/ionicons5": "^0.13.0", "axios": "^1.11.0", "dayjs": "^1.11.13", "highlight.js": "^11.11.1", "lunar-javascript": "^1.7.4", "naive-ui": "^2.42.0", "pinia": "^3.0.3", "v-calendar": "^3.1.2", "vfonts": "^0.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.3.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "terser": "^5.43.1", "typescript": "~5.8.3", "unplugin-auto-import": "^20.0.0", "unplugin-vue-components": "^29.0.0", "vite": "^7.1.2", "vue-tsc": "^3.0.5"}}