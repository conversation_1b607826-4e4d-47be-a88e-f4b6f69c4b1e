/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AddCategoryForm: typeof import('./src/components/AddCategoryForm.vue')['default']
    AddSpecificFoodForm: typeof import('./src/components/AddSpecificFoodForm.vue')['default']
    AppLayout: typeof import('./src/components/AppLayout.vue')['default']
    CreateDietPlanForm: typeof import('./src/components/CreateDietPlanForm.vue')['default']
    DashboardCalendarWidget: typeof import('./src/components/DashboardCalendarWidget.vue')['default']
    DashboardDietTracker: typeof import('./src/components/DashboardDietTracker.vue')['default']
    DietPlanCalendar: typeof import('./src/components/DietPlanCalendar.vue')['default']
    DietTypeDetail: typeof import('./src/components/DietTypeDetail.vue')['default']
    DietTypeForm: typeof import('./src/components/DietTypeForm.vue')['default']
    FoodCategoriesList: typeof import('./src/components/FoodCategoriesList.vue')['default']
    FoodCategoryForm: typeof import('./src/components/FoodCategoryForm.vue')['default']
    FoodCategoryManager: typeof import('./src/components/FoodCategoryManager.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    MobileEditFood: typeof import('./src/components/mobile/MobileEditFood.vue')['default']
    MobileEditRecord: typeof import('./src/components/mobile/MobileEditRecord.vue')['default']
    MobileFoodList: typeof import('./src/components/mobile/MobileFoodList.vue')['default']
    MobileRecordList: typeof import('./src/components/mobile/MobileRecordList.vue')['default']
    NButton: typeof import('naive-ui')['NButton']
    NCard: typeof import('naive-ui')['NCard']
    NConfigProvider: typeof import('naive-ui')['NConfigProvider']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NGrid: typeof import('naive-ui')['NGrid']
    NGridItem: typeof import('naive-ui')['NGridItem']
    NIcon: typeof import('naive-ui')['NIcon']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NPagination: typeof import('naive-ui')['NPagination']
    NSpace: typeof import('naive-ui')['NSpace']
    NTag: typeof import('naive-ui')['NTag']
    NText: typeof import('naive-ui')['NText']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
