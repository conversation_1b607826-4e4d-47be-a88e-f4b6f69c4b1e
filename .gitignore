# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Dependency directories
node_modules/
jspm_packages/

# Build outputs
dist/
dist-ssr/
build/
out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.local

# TypeScript
*.tsbuildinfo
.tsbuildinfo

# Testing
coverage/
.nyc_output/
test-results/
playwright-report/

# Cache directories
.cache/
.parcel-cache/
.vite/
.turbo/

# Temporary files
*.tmp
*.temp
.tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea/
*.swp
*.swo
*~
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Package manager files
.pnpm-debug.log*
.yarn-integrity
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Storybook build outputs
storybook-static

# Database files (if using local SQLite)
*.db
*.sqlite
*.sqlite3

# Auto-generated files (keep these as they're generated by unplugin)
# auto-imports.d.ts
# components.d.ts
