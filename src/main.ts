import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router'
import { pinia } from './stores'
import { safeInitMockData } from './utils/initMockData'

// 导入测试工具（仅在开发环境）
if (import.meta.env.DEV) {
  import('./utils/testEditFunctions')
  import('./utils/testDietFeatures')
  import('./utils/testDateFix')
}

// 通用字体
import 'vfonts/Lato.css'
// 等宽字体
import 'vfonts/FiraCode.css'

const app = createApp(App)

app.use(router)
app.use(pinia)

// 在开发环境中初始化测试数据
if (import.meta.env.DEV) {
  // 等待应用挂载后再初始化数据
  app.mount('#app')

  // 延迟初始化，确保 stores 已经准备好
  setTimeout(() => {
    safeInitMockData()
  }, 100)
} else {
  app.mount('#app')
}
