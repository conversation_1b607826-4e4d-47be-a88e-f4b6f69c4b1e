<template>
  <div class="edit-record-container">
    <!-- 移动端使用专用组件 -->
    <MobileEditRecord
      v-if="isMobile"
      :form-data="formData"
      :rules="rules"
      :loading="loading"
      :food-options="foodOptions"
      :unit-options="unitOptions"
      :nutrition-preview="nutritionPreview"
      @cancel="handleCancel"
      @save="handleSave"
      @food-type-change="handleFoodTypeChange"
      @food-change="handleFoodChange"
    />

    <!-- 桌面端使用标准表单 -->
    <NCard v-else title="编辑营养记录" :bordered="false">
      <NSpin :show="loading">
        <div v-if="!record">
          <NEmpty description="记录不存在" />
        </div>
        <div v-else>
          <NForm
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-placement="top"
            require-mark-placement="right-hanging"
          >
            <!-- 记录时间 -->
            <NGrid :cols="isMobile ? 1 : 2" :x-gap="16" responsive="screen">
              <NFormItemGi label="记录日期" path="record_date">
                <NDatePicker
                  v-model:value="formData.record_date"
                  type="date"
                  placeholder="选择日期"
                  style="width: 100%"
                />
              </NFormItemGi>
              <NFormItemGi label="记录时间" path="record_time">
                <NTimePicker
                  v-model:value="formData.record_time"
                  format="HH:mm"
                  placeholder="选择时间"
                  style="width: 100%"
                />
              </NFormItemGi>
            </NGrid>

            <!-- 食物类型选择 -->
            <NFormItem label="食物类型" path="food_type">
              <NRadioGroup v-model:value="formData.food_type" @update:value="handleFoodTypeChange">
                <NSpace>
                  <NRadio value="existing">选择已有食物</NRadio>
                  <NRadio value="custom">自定义食物</NRadio>
                </NSpace>
              </NRadioGroup>
            </NFormItem>

            <!-- 选择已有食物 -->
            <div v-if="formData.food_type === 'existing'">
              <NFormItem label="选择食物" path="food_id">
                <NSelect
                  v-model:value="formData.food_id"
                  :options="foodOptions"
                  placeholder="搜索并选择食物"
                  filterable
                  clearable
                  @update:value="handleFoodChange"
                />
              </NFormItem>
            </div>

            <!-- 自定义食物 -->
            <div v-if="formData.food_type === 'custom'">
              <NFormItem label="食物名称" path="food_name">
                <NInput
                  v-model:value="formData.food_name"
                  placeholder="请输入食物名称"
                  clearable
                />
              </NFormItem>

              <!-- 自定义营养成分 -->
              <NDivider title-placement="left">营养成分 (每100g)</NDivider>
              <NGrid :cols="isMobile ? 1 : 2" :x-gap="16" :y-gap="16" responsive="screen">
                <NFormItemGi label="热量 (千卡)">
                  <NInputNumber
                    v-model:value="formData.custom_nutrition.calories"
                    :precision="2"
                    :min="0"
                    placeholder="热量"
                    style="width: 100%"
                  />
                </NFormItemGi>
                <NFormItemGi label="蛋白质 (克)">
                  <NInputNumber
                    v-model:value="formData.custom_nutrition.protein"
                    :precision="2"
                    :min="0"
                    placeholder="蛋白质"
                    style="width: 100%"
                  />
                </NFormItemGi>
                <NFormItemGi label="碳水化合物 (克)">
                  <NInputNumber
                    v-model:value="formData.custom_nutrition.carbohydrates"
                    :precision="2"
                    :min="0"
                    placeholder="碳水化合物"
                    style="width: 100%"
                  />
                </NFormItemGi>
                <NFormItemGi label="脂肪 (克)">
                  <NInputNumber
                    v-model:value="formData.custom_nutrition.fat"
                    :precision="2"
                    :min="0"
                    placeholder="脂肪"
                    style="width: 100%"
                  />
                </NFormItemGi>
              </NGrid>
            </div>

            <!-- 食用量 -->
            <NDivider title-placement="left">食用量</NDivider>
            <NGrid :cols="isMobile ? 1 : 2" :x-gap="16" responsive="screen">
              <NFormItemGi label="数量" path="quantity">
                <NInputNumber
                  v-model:value="formData.quantity"
                  :precision="2"
                  :min="0.1"
                  placeholder="数量"
                  style="width: 100%"
                />
              </NFormItemGi>
              <NFormItemGi label="单位" path="unit">
                <NSelect
                  v-model:value="formData.unit"
                  :options="unitOptions"
                  placeholder="选择单位"
                />
              </NFormItemGi>
            </NGrid>

            <!-- 营养成分预览 -->
            <div v-if="nutritionPreview" class="nutrition-preview">
              <NDivider title-placement="left">营养成分预览</NDivider>
              <NGrid :cols="isMobile ? 2 : 4" :x-gap="16" :y-gap="16">
                <NFormItemGi>
                  <NStatistic label="热量" :value="nutritionPreview.calories" suffix="千卡" />
                </NFormItemGi>
                <NFormItemGi>
                  <NStatistic label="蛋白质" :value="nutritionPreview.protein" suffix="克" />
                </NFormItemGi>
                <NFormItemGi>
                  <NStatistic label="碳水化合物" :value="nutritionPreview.carbohydrates" suffix="克" />
                </NFormItemGi>
                <NFormItemGi>
                  <NStatistic label="脂肪" :value="nutritionPreview.fat" suffix="克" />
                </NFormItemGi>
              </NGrid>
            </div>

            <!-- 备注 -->
            <NFormItem label="备注" path="notes">
              <NInput
                v-model:value="formData.notes"
                type="textarea"
                placeholder="添加备注（可选）"
                :rows="3"
              />
            </NFormItem>

            <!-- 操作按钮 -->
            <NSpace justify="space-between" style="margin-top: 24px;">
              <NButton @click="handleCancel">取消</NButton>
              <NButton type="primary" @click="handleSave" :loading="loading">保存修改</NButton>
            </NSpace>
          </NForm>
        </div>
      </NSpin>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useResponsive } from '../composables/useResponsive'
import MobileEditRecord from '../components/mobile/MobileEditRecord.vue'
import dayjs from 'dayjs'
import {
  NCard,
  NSpin,
  NEmpty,
  NForm,
  NFormItem,
  NFormItemGi,
  NInput,
  NInputNumber,
  NSelect,
  NRadioGroup,
  NRadio,
  NDatePicker,
  NTimePicker,
  NButton,
  NSpace,
  NGrid,
  NDivider,
  NStatistic,
  type FormInst,
  type FormRules
} from 'naive-ui'
import { useFoodStore } from '../stores/food'
import { useNutritionStore, type NutritionRecord } from '../stores/nutrition'

const route = useRoute()
const router = useRouter()
const message = useMessage()
const foodStore = useFoodStore()
const nutritionStore = useNutritionStore()
const { isMobile } = useResponsive()

const formRef = ref<FormInst | null>(null)
const loading = ref(false)
const record = ref<NutritionRecord | null>(null)

// 表单数据
const formData = reactive({
  record_date: Date.now(),
  record_time: Date.now(),
  food_type: 'existing' as 'existing' | 'custom',
  food_id: null as number | null,
  food_name: '',
  quantity: 100,
  unit: '克',
  custom_nutrition: {
    calories: 0,
    protein: 0,
    carbohydrates: 0,
    fat: 0
  },
  notes: ''
})

// 表单验证规则
const rules: FormRules = {
  record_date: [
    { required: true, message: '请选择记录日期', trigger: 'blur' }
  ],
  record_time: [
    { required: true, message: '请选择记录时间', trigger: 'blur' }
  ],
  food_id: [
    { 
      required: true, 
      message: '请选择食物', 
      trigger: 'change',
      validator: () => formData.food_type === 'existing' ? formData.food_id !== null : true
    }
  ],
  food_name: [
    { 
      required: true, 
      message: '请输入食物名称', 
      trigger: 'blur',
      validator: () => formData.food_type === 'custom' ? formData.food_name.trim() !== '' : true
    }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' }
  ]
}

// 食物选项
const foodOptions = computed(() => {
  return foodStore.foods.map(food => ({
    label: `${food.name}${food.brand ? ` (${food.brand})` : ''}`,
    value: food.id
  }))
})

// 单位选项
const unitOptions = [
  { label: '克', value: '克' },
  { label: '毫升', value: '毫升' },
  { label: '个', value: '个' },
  { label: '片', value: '片' },
  { label: '勺', value: '勺' },
  { label: '杯', value: '杯' },
  { label: '碗', value: '碗' }
]

// 选中的食物
const selectedFood = computed(() => {
  if (formData.food_type === 'existing' && formData.food_id) {
    return foodStore.foods.find(f => f.id === formData.food_id)
  }
  return null
})

// 营养成分预览
const nutritionPreview = computed(() => {
  if (formData.food_type === 'custom') {
    const ratio = formData.quantity / 100
    return {
      calories: Math.round(formData.custom_nutrition.calories * ratio * 100) / 100,
      protein: Math.round(formData.custom_nutrition.protein * ratio * 100) / 100,
      carbohydrates: Math.round(formData.custom_nutrition.carbohydrates * ratio * 100) / 100,
      fat: Math.round(formData.custom_nutrition.fat * ratio * 100) / 100
    }
  }

  if (!selectedFood.value) return null

  const food = selectedFood.value
  const ratio = formData.quantity / 100

  return {
    calories: Math.round(food.calories * ratio * 100) / 100,
    protein: Math.round(food.protein * ratio * 100) / 100,
    carbohydrates: Math.round(food.carbohydrates * ratio * 100) / 100,
    fat: Math.round(food.total_fat * ratio * 100) / 100
  }
})

const handleFoodTypeChange = () => {
  // 切换食物类型时重置相关字段
  formData.food_id = null
  formData.food_name = ''
  // 重置自定义营养成分
  Object.keys(formData.custom_nutrition).forEach(key => {
    formData.custom_nutrition[key as keyof typeof formData.custom_nutrition] = 0
  })
}

const handleFoodChange = () => {
  // 食物变化时可以做一些处理
}

const handleCancel = () => {
  router.back()
}

// 保存记录
const handleSave = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    // 构建记录数据
    const recordData = {
      ...record.value,
      record_date: dayjs(formData.record_date).format('YYYY-MM-DD'),
      record_time: dayjs(formData.record_time).format('HH:mm'),
      food_id: formData.food_type === 'existing' ? formData.food_id : undefined,
      food_name: formData.food_type === 'custom' ? formData.food_name : undefined,
      quantity: formData.quantity,
      unit: formData.unit,
      notes: formData.notes
    }

    // 计算实际营养成分
    if (formData.food_type === 'existing' && selectedFood.value) {
      const food = selectedFood.value
      const ratio = formData.quantity / 100

      recordData.actual_calories = Math.round(food.calories * ratio * 100) / 100
      recordData.actual_protein = Math.round(food.protein * ratio * 100) / 100
      recordData.actual_carbohydrates = Math.round(food.carbohydrates * ratio * 100) / 100
      recordData.actual_total_fat = Math.round(food.total_fat * ratio * 100) / 100
      recordData.actual_saturated_fat = Math.round(food.saturated_fat * ratio * 100) / 100
      recordData.actual_trans_fat = Math.round(food.trans_fat * ratio * 100) / 100
      recordData.actual_cholesterol = Math.round(food.cholesterol * ratio * 100) / 100
      recordData.actual_sodium = Math.round(food.sodium * ratio * 100) / 100
      recordData.actual_potassium = Math.round(food.potassium * ratio * 100) / 100
      recordData.actual_dietary_fiber = Math.round(food.dietary_fiber * ratio * 100) / 100
      recordData.actual_sugar = Math.round(food.sugar * ratio * 100) / 100
      recordData.actual_vitamin_a = Math.round(food.vitamin_a * ratio * 100) / 100
      recordData.actual_vitamin_c = Math.round(food.vitamin_c * ratio * 100) / 100
      recordData.actual_calcium = Math.round(food.calcium * ratio * 100) / 100
      recordData.actual_iron = Math.round(food.iron * ratio * 100) / 100
    } else if (formData.food_type === 'custom') {
      const ratio = formData.quantity / 100

      recordData.actual_calories = Math.round(formData.custom_nutrition.calories * ratio * 100) / 100
      recordData.actual_protein = Math.round(formData.custom_nutrition.protein * ratio * 100) / 100
      recordData.actual_carbohydrates = Math.round(formData.custom_nutrition.carbohydrates * ratio * 100) / 100
      recordData.actual_total_fat = Math.round(formData.custom_nutrition.fat * ratio * 100) / 100
      // 其他营养成分设为0
      recordData.actual_saturated_fat = 0
      recordData.actual_trans_fat = 0
      recordData.actual_cholesterol = 0
      recordData.actual_sodium = 0
      recordData.actual_potassium = 0
      recordData.actual_dietary_fiber = 0
      recordData.actual_sugar = 0
      recordData.actual_vitamin_a = 0
      recordData.actual_vitamin_c = 0
      recordData.actual_calcium = 0
      recordData.actual_iron = 0
    }

    // 这里应该调用API更新记录
    console.log('更新记录数据:', recordData)

    // 更新store中的数据
    if (record.value?.id) {
      nutritionStore.updateRecord(record.value.id, recordData)
    }

    // 暂时模拟保存成功
    await new Promise(resolve => setTimeout(resolve, 1000))

    message.success('记录更新成功')
    router.back()
  } catch (error) {
    message.error('请检查表单填写是否正确')
  } finally {
    loading.value = false
  }
}

const loadRecord = async () => {
  const id = Number(route.params.id)
  if (id) {
    loading.value = true
    try {
      // 这里应该调用API加载记录详情
      record.value = nutritionStore.records.find(r => r.id === id) || null

      if (record.value) {
        // 填充表单数据
        formData.record_date = dayjs(record.value.record_date).valueOf()
        formData.record_time = dayjs(`${record.value.record_date} ${record.value.record_time}`).valueOf()
        formData.food_type = record.value.food_id ? 'existing' : 'custom'
        formData.food_id = record.value.food_id || null
        formData.food_name = record.value.food_name || ''
        formData.quantity = record.value.quantity
        formData.unit = record.value.unit
        formData.notes = record.value.notes || ''

        // 如果是自定义食物，填充营养成分
        if (!record.value.food_id) {
          const ratio = 100 / record.value.quantity // 反推每100g的营养成分
          formData.custom_nutrition.calories = Math.round(record.value.actual_calories / ratio * 100) / 100
          formData.custom_nutrition.protein = Math.round(record.value.actual_protein / ratio * 100) / 100
          formData.custom_nutrition.carbohydrates = Math.round(record.value.actual_carbohydrates / ratio * 100) / 100
          formData.custom_nutrition.fat = Math.round(record.value.actual_total_fat / ratio * 100) / 100
        }
      }
    } finally {
      loading.value = false
    }
  }
}

onMounted(() => {
  loadRecord()
})
</script>

<style scoped>
.edit-record-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0;
}

.nutrition-preview {
  margin-top: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .nutrition-preview {
    padding: 12px;
    margin-top: 12px;
  }

  /* 移动端表单项间距调整 */
  .n-form-item {
    margin-bottom: 16px;
  }

  /* 移动端输入框样式 */
  .n-input-number {
    width: 100% !important;
  }
}
</style>
