<template>
  <div class="daily-diet-tracker-page">
    <div class="page-header">
      <h1>每日饮食跟踪</h1>
      <div class="header-info">
        <div class="date-info">
          <h2>{{ formatDate(todayString) }}</h2>
          <p>{{ getWeekday(todayString) }}</p>
        </div>
        <div class="progress-info" v-if="todayPlan">
          <NProgress
            type="circle"
            :percentage="completionRate"
            :stroke-width="8"
            :show-indicator="false"
            style="width: 60px;"
          />
          <div class="progress-text">
            <span class="progress-number">{{ completionRate }}%</span>
            <span class="progress-label">完成度</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 今日计划概览 -->
    <div v-if="todayPlan" class="plan-overview">
      <NCard class="overview-card">
        <template #header>
          <div class="plan-header">
            <div class="plan-info">
              <h3>{{ todayPlan.planName || '今日饮食计划' }}</h3>
              <div v-if="selectedDietType" class="diet-type-badge">
                <div 
                  class="color-indicator" 
                  :style="{ backgroundColor: selectedDietType.color }"
                ></div>
                <span>{{ selectedDietType.name }}</span>
              </div>
            </div>
            <div class="plan-stats">
              <div class="stat-item">
                <span class="stat-number">{{ consumedCount }}</span>
                <span class="stat-label">已摄入</span>
              </div>
              <div class="stat-divider">/</div>
              <div class="stat-item">
                <span class="stat-number">{{ totalCount }}</span>
                <span class="stat-label">总计</span>
              </div>
            </div>
          </div>
        </template>

        <div v-if="todayPlan.notes" class="plan-notes">
          <p>{{ todayPlan.notes }}</p>
        </div>
      </NCard>
    </div>

    <!-- 食物分类跟踪列表 -->
    <div v-if="todayPlan" class="tracking-section">
      <div class="section-header">
        <h3>食物分类跟踪</h3>
        <div class="filter-actions">
          <NButtonGroup>
            <NButton
              :type="filter === 'all' ? 'primary' : 'default'"
              @click="filter = 'all'"
            >
              全部 ({{ totalCount }})
            </NButton>
            <NButton
              :type="filter === 'consumed' ? 'primary' : 'default'"
              @click="filter = 'consumed'"
            >
              已摄入 ({{ consumedCount }})
            </NButton>
            <NButton
              :type="filter === 'pending' ? 'primary' : 'default'"
              @click="filter = 'pending'"
            >
              待摄入 ({{ pendingCount }})
            </NButton>
          </NButtonGroup>
        </div>
      </div>

      <div class="tracking-list">
        <div
          v-for="item in filteredItems"
          :key="item.id"
          class="tracking-item"
          :class="{ consumed: item.isConsumed }"
        >
          <div class="item-checkbox">
            <NCheckbox
              :checked="item.isConsumed"
              size="large"
              @update:checked="(checked) => toggleConsumption(item.id, checked)"
            />
          </div>

          <div class="item-content">
            <div class="item-header">
              <h4>{{ item.categoryName }}</h4>
              <div class="item-status">
                <NTag
                  :type="item.isConsumed ? 'success' : 'default'"
                  size="small"
                >
                  {{ item.isConsumed ? '已摄入' : '待摄入' }}
                </NTag>
                <span v-if="item.consumedAt" class="consumed-time">
                  {{ formatTime(item.consumedAt) }}
                </span>
              </div>
            </div>

            <p v-if="item.categoryDescription" class="item-description">
              {{ item.categoryDescription }}
            </p>

            <!-- 具体食物列表 -->
            <div v-if="item.specificFoods && item.specificFoods.length > 0" class="specific-foods">
              <div class="foods-header">
                <span class="foods-label">具体食物：</span>
              </div>
              <div class="foods-list">
                <div
                  v-for="food in item.specificFoods"
                  :key="food.id"
                  class="food-tag"
                >
                  <span class="food-name">{{ food.name }}</span>
                  <span v-if="food.amount && food.unit" class="food-amount">
                    {{ food.amount }}{{ food.unit }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 备注 -->
            <div v-if="item.notes" class="item-notes">
              <span class="notes-label">备注：</span>
              <span class="notes-content">{{ item.notes }}</span>
            </div>
          </div>

          <div class="item-actions">
            <NButton
              quaternary
              circle
              size="small"
              @click="addNote(item)"
            >
              <template #icon>
                <NIcon><DocumentTextOutline /></NIcon>
              </template>
            </NButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <NEmpty description="今日暂无饮食计划">
        <template #extra>
          <NButton type="primary" @click="$router.push('/diet-plan')">
            创建今日计划
          </NButton>
        </template>
      </NEmpty>
    </div>

    <!-- 每日总结 -->
    <div v-if="todayPlan && completionRate === 100" class="daily-summary">
      <NCard class="summary-card">
        <template #header>
          <div class="summary-header">
            <NIcon size="24" color="#52c41a">
              <CheckmarkCircleOutline />
            </NIcon>
            <h3>今日饮食计划完成！</h3>
          </div>
        </template>
        <div class="summary-content">
          <p>恭喜您完成了今日的饮食计划！坚持健康饮食，让身体更加健康。</p>
          <div class="summary-stats">
            <div class="summary-stat">
              <span class="stat-value">{{ totalCount }}</span>
              <span class="stat-name">食物分类</span>
            </div>
            <div class="summary-stat">
              <span class="stat-value">{{ getTotalFoodsCount() }}</span>
              <span class="stat-name">具体食物</span>
            </div>
          </div>
        </div>
      </NCard>
    </div>

    <!-- 添加备注模态框 -->
    <NModal v-model:show="showNoteModal" preset="dialog" title="添加备注">
      <div class="note-form">
        <NInput
          v-model:value="noteText"
          type="textarea"
          placeholder="请输入备注内容"
          :rows="3"
          maxlength="200"
          show-count
        />
        <div class="note-actions">
          <NSpace>
            <NButton @click="showNoteModal = false">取消</NButton>
            <NButton type="primary" @click="saveNote">保存</NButton>
          </NSpace>
        </div>
      </div>
    </NModal>

    <!-- 加载状态 -->
    <NSpin :show="loading" style="width: 100%;">
      <div style="height: 200px;"></div>
    </NSpin>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  NCard,
  NCheckbox,
  NTag,
  NButton,
  NButtonGroup,
  NProgress,
  NEmpty,
  NModal,
  NInput,
  NSpace,
  NIcon,
  NSpin,
  useMessage
} from 'naive-ui'
import {
  DocumentTextOutline,
  CheckmarkCircleOutline
} from '@vicons/ionicons5'
import { useDailyDietPlanStore, type DailyDietPlanItem } from '../stores/dailyDietPlan'
import { useDietTypeStore } from '../stores/dietType'

const dailyPlanStore = useDailyDietPlanStore()
const dietTypeStore = useDietTypeStore()
const message = useMessage()

// 响应式状态
const filter = ref<'all' | 'consumed' | 'pending'>('all')
const showNoteModal = ref(false)
const noteText = ref('')
const editingItem = ref<DailyDietPlanItem | null>(null)

// 计算属性
const todayString = computed(() => {
  return new Date().toISOString().split('T')[0]
})

const todayPlan = computed(() => dailyPlanStore.getTodayPlan)
const todayPlanItems = computed(() => dailyPlanStore.getTodayPlanItems)
const loading = computed(() => dailyPlanStore.loading || dietTypeStore.loading)

const selectedDietType = computed(() => {
  if (!todayPlan.value?.dietTypeId) return null
  return dietTypeStore.getDietTypeById(todayPlan.value.dietTypeId)
})

const consumedCount = computed(() => {
  return todayPlanItems.value.filter(item => item.isConsumed).length
})

const totalCount = computed(() => todayPlanItems.value.length)

const pendingCount = computed(() => totalCount.value - consumedCount.value)

const completionRate = computed(() => {
  if (totalCount.value === 0) return 0
  return Math.round((consumedCount.value / totalCount.value) * 100)
})

const filteredItems = computed(() => {
  switch (filter.value) {
    case 'consumed':
      return todayPlanItems.value.filter(item => item.isConsumed)
    case 'pending':
      return todayPlanItems.value.filter(item => !item.isConsumed)
    default:
      return todayPlanItems.value
  }
})

// 方法
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: 'long',
    day: 'numeric'
  })
}

const getWeekday = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    weekday: 'long'
  })
}

const formatTime = (timeString: string) => {
  const time = new Date(timeString)
  return time.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getTotalFoodsCount = () => {
  return todayPlanItems.value.reduce((total, item) => {
    return total + (item.specificFoods?.length || 0)
  }, 0)
}

const toggleConsumption = async (itemId: number, isConsumed: boolean) => {
  try {
    await dailyPlanStore.toggleItemConsumption(itemId, isConsumed)
    message.success(isConsumed ? '已标记为摄入' : '已取消摄入标记')
  } catch (error) {
    message.error(error instanceof Error ? error.message : '操作失败')
  }
}

const addNote = (item: DailyDietPlanItem) => {
  editingItem.value = item
  noteText.value = item.notes || ''
  showNoteModal.value = true
}

const saveNote = async () => {
  if (!editingItem.value) return
  
  try {
    // TODO: 实现保存备注功能
    message.success('备注保存成功')
    showNoteModal.value = false
    editingItem.value = null
    noteText.value = ''
  } catch (error) {
    message.error(error instanceof Error ? error.message : '保存失败')
  }
}

// 生命周期
onMounted(async () => {
  await dietTypeStore.fetchDietTypes()
  await dailyPlanStore.fetchDailyPlans(1) // 假设用户ID为1
  
  const plan = todayPlan.value
  if (plan) {
    await dailyPlanStore.fetchPlanItems(plan.id)
  }
})
</script>

<style scoped>
.daily-diet-tracker-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.date-info {
  text-align: right;
}

.date-info h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.date-info p {
  margin: 4px 0 0 0;
  color: #666;
  font-size: 14px;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-number {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.progress-label {
  font-size: 12px;
  color: #666;
}

.plan-overview {
  margin-bottom: 24px;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.plan-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.diet-type-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
}

.color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.plan-stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.stat-divider {
  font-size: 16px;
  color: #ccc;
  margin: 0 4px;
}

.plan-notes p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.tracking-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.tracking-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tracking-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.tracking-item:hover {
  border-color: #d0d0d0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tracking-item.consumed {
  background: #f6ffed;
  border-color: #b7eb8f;
}

.item-checkbox {
  flex-shrink: 0;
  margin-top: 2px;
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.item-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.item-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.consumed-time {
  font-size: 12px;
  color: #666;
}

.item-description {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.specific-foods {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.foods-header {
  display: flex;
  align-items: center;
}

.foods-label {
  font-size: 12px;
  font-weight: 600;
  color: #666;
}

.foods-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.food-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #f0f0f0;
  border-radius: 4px;
  font-size: 12px;
}

.food-name {
  color: #333;
}

.food-amount {
  color: #1890ff;
  font-weight: 500;
}

.item-notes {
  display: flex;
  gap: 6px;
  font-size: 14px;
}

.notes-label {
  color: #666;
  font-weight: 500;
}

.notes-content {
  color: #333;
}

.item-actions {
  flex-shrink: 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.daily-summary {
  margin-top: 32px;
}

.summary-card {
  background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
  border: 1px solid #b7eb8f;
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.summary-header h3 {
  margin: 0;
  color: #52c41a;
  font-size: 18px;
  font-weight: 600;
}

.summary-content p {
  margin: 0 0 16px 0;
  color: #333;
  line-height: 1.6;
}

.summary-stats {
  display: flex;
  gap: 24px;
}

.summary-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #52c41a;
}

.stat-name {
  font-size: 14px;
  color: #666;
}

.note-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.note-actions {
  display: flex;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-info {
    justify-content: space-between;
  }
  
  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .tracking-item {
    flex-direction: column;
    gap: 12px;
  }
  
  .item-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .plan-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .summary-stats {
    justify-content: center;
  }
}
</style>
