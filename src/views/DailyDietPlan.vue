<template>
  <div class="daily-diet-plan-page">
    <div class="page-header">
      <h1>每日饮食计划</h1>
      <div class="header-actions">
        <NDatePicker
          v-model:value="selectedDate"
          type="date"
          placeholder="选择日期"
          @update:value="handleDateChange"
        />
        <NButton type="primary" @click="showCreateModal = true">
          <template #icon>
            <NIcon><AddOutline /></NIcon>
          </template>
          创建计划
        </NButton>
      </div>
    </div>

    <!-- 当前计划概览 -->
    <div v-if="currentPlan" class="plan-overview">
      <NCard class="overview-card">
        <template #header>
          <div class="plan-header">
            <div class="plan-info">
              <h2>{{ currentPlan.planName || '我的饮食计划' }}</h2>
              <p class="plan-date">{{ formatDate(currentPlan.planDate) }}</p>
            </div>
            <div class="plan-actions">
              <NButton quaternary @click="editPlan">
                <template #icon>
                  <NIcon><CreateOutline /></NIcon>
                </template>
                编辑
              </NButton>
            </div>
          </div>
        </template>

        <div class="plan-content">
          <div v-if="selectedDietType" class="diet-type-info">
            <div class="diet-type-badge">
              <div 
                class="color-indicator" 
                :style="{ backgroundColor: selectedDietType.color }"
              ></div>
              <span>{{ selectedDietType.name }}</span>
            </div>
            <p class="diet-type-description">{{ selectedDietType.description }}</p>
          </div>

          <div v-if="currentPlan.notes" class="plan-notes">
            <h4>备注</h4>
            <p>{{ currentPlan.notes }}</p>
          </div>
        </div>
      </NCard>
    </div>

    <!-- 食物分类列表 -->
    <div v-if="currentPlan" class="food-categories-section">
      <div class="section-header">
        <h3>食物分类</h3>
        <div class="category-actions">
          <NButton @click="showAddCategoryModal = true">
            <template #icon>
              <NIcon><AddOutline /></NIcon>
            </template>
            添加分类
          </NButton>
        </div>
      </div>

      <div class="categories-grid">
        <NCard
          v-for="item in planItems"
          :key="item.id"
          class="category-card"
          :class="{ consumed: item.isConsumed }"
        >
          <template #header>
            <div class="category-header">
              <div class="category-info">
                <h4>{{ item.categoryName }}</h4>
                <p v-if="item.categoryDescription" class="category-desc">
                  {{ item.categoryDescription }}
                </p>
              </div>
              <div class="category-actions">
                <NButton
                  quaternary
                  circle
                  size="small"
                  @click="addSpecificFood(item)"
                >
                  <template #icon>
                    <NIcon><AddOutline /></NIcon>
                  </template>
                </NButton>
              </div>
            </div>
          </template>

          <div class="category-content">
            <!-- 具体食物列表 -->
            <div v-if="item.specificFoods && item.specificFoods.length > 0" class="specific-foods">
              <div
                v-for="food in item.specificFoods"
                :key="food.id"
                class="food-item"
              >
                <div class="food-info">
                  <span class="food-name">{{ food.name }}</span>
                  <span v-if="food.description" class="food-desc">{{ food.description }}</span>
                  <span v-if="food.amount && food.unit" class="food-amount">
                    {{ food.amount }}{{ food.unit }}
                  </span>
                </div>
                <NButton
                  quaternary
                  circle
                  size="tiny"
                  type="error"
                  @click="removeSpecificFood(item.id, food.id)"
                >
                  <template #icon>
                    <NIcon size="12"><CloseOutline /></NIcon>
                  </template>
                </NButton>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="empty-foods">
              <p>暂无具体食物，点击上方 + 号添加</p>
            </div>
          </div>

          <template #action>
            <div class="card-footer">
              <NCheckbox
                :checked="item.isConsumed"
                @update:checked="(checked) => toggleConsumption(item.id, checked)"
              >
                {{ item.isConsumed ? '已摄入' : '未摄入' }}
              </NCheckbox>
              <span v-if="item.consumedAt" class="consumed-time">
                {{ formatTime(item.consumedAt) }}
              </span>
            </div>
          </template>
        </NCard>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <NEmpty description="暂无饮食计划">
        <template #extra>
          <NButton type="primary" @click="showCreateModal = true">
            创建今日计划
          </NButton>
        </template>
      </NEmpty>
    </div>

    <!-- 创建计划模态框 -->
    <NModal v-model:show="showCreateModal" preset="card" style="width: 600px;" title="创建饮食计划">
      <CreateDietPlanForm
        :selected-date="selectedDateString"
        @save="handleCreatePlan"
        @cancel="showCreateModal = false"
      />
    </NModal>

    <!-- 添加具体食物模态框 -->
    <NModal v-model:show="showAddFoodModal" preset="dialog" title="添加具体食物">
      <AddSpecificFoodForm
        v-if="editingItem"
        :category-name="editingItem.categoryName"
        @save="handleAddSpecificFood"
        @cancel="showAddFoodModal = false"
      />
    </NModal>

    <!-- 编辑计划模态框 -->
    <NModal v-model:show="showEditModal" preset="card" style="width: 600px;" title="编辑饮食计划">
      <CreateDietPlanForm
        v-if="editingPlan"
        :selected-date="selectedDateString"
        :initial-data="editingPlan"
        @save="handleEditPlan"
        @cancel="showEditModal = false"
      />
    </NModal>

    <!-- 添加分类模态框 -->
    <NModal v-model:show="showAddCategoryModal" preset="dialog" title="添加食物分类">
      <AddCategoryForm
        @save="handleAddCategory"
        @cancel="showAddCategoryModal = false"
      />
    </NModal>

    <!-- 加载状态 -->
    <NSpin :show="loading" style="width: 100%;">
      <div style="height: 200px;"></div>
    </NSpin>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import {
  NCard,
  NButton,
  NIcon,
  NDatePicker,
  NModal,
  NCheckbox,
  NEmpty,
  NSpin,
  useMessage
} from 'naive-ui'
import {
  AddOutline,
  CreateOutline,
  CloseOutline
} from '@vicons/ionicons5'
import { useDailyDietPlanStore, type DailyDietPlanItem } from '../stores/dailyDietPlan'
import { useDietTypeStore } from '../stores/dietType'
import CreateDietPlanForm from '../components/CreateDietPlanForm.vue'
import AddSpecificFoodForm from '../components/AddSpecificFoodForm.vue'
import AddCategoryForm from '../components/AddCategoryForm.vue'

const dailyPlanStore = useDailyDietPlanStore()
const dietTypeStore = useDietTypeStore()
const message = useMessage()

// 响应式状态
const selectedDate = ref<number>(Date.now())
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showAddCategoryModal = ref(false)
const showAddFoodModal = ref(false)
const editingItem = ref<DailyDietPlanItem | null>(null)
const editingPlan = ref<any>(null)

// 计算属性
const selectedDateString = computed(() => {
  return new Date(selectedDate.value).toISOString().split('T')[0]
})

const currentPlan = computed(() => {
  return dailyPlanStore.getPlanByDate(selectedDateString.value)
})

const planItems = computed(() => {
  return currentPlan.value ? dailyPlanStore.getPlanItemsByPlanId(currentPlan.value.id) : []
})

const selectedDietType = computed(() => {
  if (!currentPlan.value?.dietTypeId) return null
  return dietTypeStore.getDietTypeById(currentPlan.value.dietTypeId)
})

const loading = computed(() => dailyPlanStore.loading || dietTypeStore.loading)

// 方法
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

const formatTime = (timeString: string) => {
  const time = new Date(timeString)
  return time.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleDateChange = async () => {
  await loadPlanData()
}

const loadPlanData = async () => {
  const plan = dailyPlanStore.getPlanByDate(selectedDateString.value)
  if (plan) {
    await dailyPlanStore.fetchPlanItems(plan.id)
  }
}

const editPlan = () => {
  if (!currentPlan.value) return
  editingPlan.value = { ...currentPlan.value }
  showEditModal.value = true
}

const addSpecificFood = (item: DailyDietPlanItem) => {
  editingItem.value = item
  showAddFoodModal.value = true
}

const toggleConsumption = async (itemId: number, isConsumed: boolean) => {
  try {
    await dailyPlanStore.toggleItemConsumption(itemId, isConsumed)
    message.success(isConsumed ? '已标记为摄入' : '已取消摄入标记')
  } catch (error) {
    message.error(error instanceof Error ? error.message : '操作失败')
  }
}

const removeSpecificFood = async (itemId: number, foodId: string) => {
  try {
    await dailyPlanStore.removeSpecificFoodFromItem(itemId, foodId)
    message.success('删除成功')
  } catch (error) {
    message.error(error instanceof Error ? error.message : '删除失败')
  }
}

const handleCreatePlan = async (planData: any) => {
  try {
    const newPlan = await dailyPlanStore.createDailyPlan({
      ...planData,
      userId: 1, // 假设当前用户ID为1
      planDate: selectedDateString.value
    })

    // 如果选择了饮食类型并且勾选了自动创建分类，创建对应的食物分类项目
    if (planData.dietTypeId && planData.autoCreateCategories) {
      const categories = dietTypeStore.getFoodCategoriesByDietType(planData.dietTypeId)
      for (const category of categories) {
        await createPlanItem(newPlan.id, {
          categoryName: category.categoryName,
          categoryDescription: category.description,
          sortOrder: category.sortOrder
        })
      }
    }

    showCreateModal.value = false
    message.success('创建成功')
    await loadPlanData()
  } catch (error) {
    message.error(error instanceof Error ? error.message : '创建失败')
  }
}

const handleEditPlan = async (planData: any) => {
  if (!editingPlan.value) return

  try {
    await dailyPlanStore.updateDailyPlan(editingPlan.value.id, {
      planName: planData.planName,
      dietTypeId: planData.dietTypeId,
      notes: planData.notes
    })

    showEditModal.value = false
    editingPlan.value = null
    message.success('更新成功')
    await loadPlanData()
  } catch (error) {
    message.error(error instanceof Error ? error.message : '更新失败')
  }
}

const handleAddSpecificFood = async (foodData: any) => {
  if (!editingItem.value) return

  try {
    await dailyPlanStore.addSpecificFoodToItem(editingItem.value.id, {
      id: Date.now().toString(),
      ...foodData
    })
    showAddFoodModal.value = false
    editingItem.value = null
    message.success('添加成功')
  } catch (error) {
    message.error(error instanceof Error ? error.message : '添加失败')
  }
}

const handleAddCategory = async (categoryData: any) => {
  if (!currentPlan.value) return

  try {
    await createPlanItem(currentPlan.value.id, {
      categoryName: categoryData.categoryName,
      categoryDescription: categoryData.categoryDescription,
      sortOrder: planItems.value.length
    })
    showAddCategoryModal.value = false
    message.success('添加分类成功')
    await loadPlanData()
  } catch (error) {
    message.error(error instanceof Error ? error.message : '添加分类失败')
  }
}

const createPlanItem = async (planId: number, itemData: {
  categoryName: string
  categoryDescription?: string
  sortOrder: number
}) => {
  // 模拟创建计划项目的API调用
  const newItem = {
    id: Math.max(...planItems.value.map(item => item.id), 0) + 1,
    dailyDietPlanId: planId,
    categoryName: itemData.categoryName,
    categoryDescription: itemData.categoryDescription,
    specificFoods: [],
    isConsumed: false,
    sortOrder: itemData.sortOrder,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }

  // 直接添加到本地状态中（在真实应用中应该调用API）
  dailyPlanStore.planItems.push(newItem)
}

// 监听选中日期变化
watch(selectedDateString, async () => {
  await loadPlanData()
})

// 生命周期
onMounted(async () => {
  await dietTypeStore.fetchDietTypes()
  await dietTypeStore.fetchFoodCategories()
  await dailyPlanStore.fetchDailyPlans(1) // 假设用户ID为1
  await loadPlanData()
})
</script>

<style scoped>
.daily-diet-plan-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.plan-overview {
  margin-bottom: 24px;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.plan-info h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.plan-date {
  margin: 4px 0 0 0;
  color: #666;
  font-size: 14px;
}

.diet-type-info {
  margin-bottom: 16px;
}

.diet-type-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.diet-type-description {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.plan-notes h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.plan-notes p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.food-categories-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.category-card {
  transition: all 0.2s ease;
}

.category-card.consumed {
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.category-info h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.category-desc {
  margin: 4px 0 0 0;
  font-size: 12px;
  color: #666;
}

.specific-foods {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.food-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px;
  background: #fafafa;
  border-radius: 6px;
  gap: 8px;
}

.food-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.food-name {
  font-weight: 500;
  font-size: 14px;
}

.food-desc {
  font-size: 12px;
  color: #666;
}

.food-amount {
  font-size: 12px;
  color: #1890ff;
  font-weight: 500;
}

.empty-foods {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.consumed-time {
  font-size: 12px;
  color: #666;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: space-between;
  }
  
  .categories-grid {
    grid-template-columns: 1fr;
  }
  
  .plan-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}
</style>
