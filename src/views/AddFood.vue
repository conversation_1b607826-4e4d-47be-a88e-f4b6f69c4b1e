<template>
  <div class="add-food-container">
    <NCard title="添加新食物" :bordered="false">
      <NForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="top"
        require-mark-placement="right-hanging"
      >
        <!-- 基本信息 -->
        <NFormItem label="输入食物名称" path="name">
          <NInput
            v-model:value="formData.name"
            placeholder="请输入食物名称"
            clearable
          />
        </NFormItem>

        <NGrid :cols="2" :x-gap="16" responsive="screen">
          <NFormItemGi label="品牌" path="brand">
            <NInput
              v-model:value="formData.brand"
              placeholder="品牌（可选）"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="条形码" path="barcode">
            <NInput
              v-model:value="formData.barcode"
              placeholder="条形码（可选）"
              clearable
            />
          </NFormItemGi>
        </NGrid>

         <!-- 食物标签 -->
        <NDivider title-placement="left">食物标签</NDivider>
        <NFormItem label="标签" path="tagIds">
          <NSelect
            v-model:value="formData.tagIds"
            multiple
            :options="tagOptions"
            placeholder="选择标签"
            clearable
          />
        </NFormItem>

        <!-- 营养成分 (每100g) -->
        <NDivider title-placement="left">
          <NSpace align="center">
            <span>营养成分 (每100g)</span>
            <NButton
              size="small"
              text
              type="info"
              @click="autoCalculateAllNRV"
            >
              自动计算NRV%
            </NButton>
            <NButton
              v-if="isMobile"
              size="small"
              text
              type="primary"
              @click="showMoreNutrition = !showMoreNutrition"
            >
              {{ showMoreNutrition ? '收起' : '显示更多' }}
            </NButton>
          </NSpace>
        </NDivider>

        <!-- 基础营养成分 -->
        <NGrid :cols="isMobile ? 1 : 2" :x-gap="16" :y-gap="16" responsive="screen">
          <!-- 热量 -->
          <NFormItemGi>
            <template #label>
              <span>每份热量</span>
              <NText depth="3" style="font-size: 12px; margin-left: 8px;">(基于2000千卡)</NText>
            </template>
            <div class="nutrition-input-group">
              <NInputNumber
                v-model:value="formData.calories"
                :precision="2"
                :min="0"
                placeholder="含量"
                class="nutrition-value-input"
              >
                <template #suffix>千卡</template>
              </NInputNumber>
              <NInputNumber
                v-model:value="formData.calories_nrv"
                :precision="2"
                :min="0"
                :max="999"
                placeholder="NRV%"
                class="nutrition-nrv-input"
              >
                <template #suffix>%NRV</template>
              </NInputNumber>
            </div>
          </NFormItemGi>

          <!-- 碳水化合物 -->
          <NFormItemGi>
            <template #label>
              <span>总碳水化合物</span>
              <NText depth="3" style="font-size: 12px; margin-left: 8px;">(基于300g)</NText>
            </template>
            <div class="nutrition-input-group">
              <NInputNumber
                v-model:value="formData.carbohydrates"
                :precision="2"
                :min="0"
                placeholder="含量"
                class="nutrition-value-input"
              >
                <template #suffix>克</template>
              </NInputNumber>
              <NInputNumber
                v-model:value="formData.carbohydrates_nrv"
                :precision="2"
                :min="0"
                :max="999"
                placeholder="NRV%"
                class="nutrition-nrv-input"
              >
                <template #suffix>%NRV</template>
              </NInputNumber>
            </div>
          </NFormItemGi>

          <!-- 总脂肪 -->
          <NFormItemGi>
            <template #label>
              <span>总脂肪</span>
              <NText depth="3" style="font-size: 12px; margin-left: 8px;">(基于60g)</NText>
            </template>
            <div class="nutrition-input-group">
              <NInputNumber
                v-model:value="formData.total_fat"
                :precision="2"
                :min="0"
                placeholder="含量"
                class="nutrition-value-input"
              >
                <template #suffix>克</template>
              </NInputNumber>
              <NInputNumber
                v-model:value="formData.total_fat_nrv"
                :precision="2"
                :min="0"
                :max="999"
                placeholder="NRV%"
                class="nutrition-nrv-input"
              >
                <template #suffix>%NRV</template>
              </NInputNumber>
            </div>
          </NFormItemGi>

          <!-- 蛋白质 -->
          <NFormItemGi>
            <template #label>
              <span>蛋白质</span>
              <NText depth="3" style="font-size: 12px; margin-left: 8px;">(基于60g)</NText>
            </template>
            <div class="nutrition-input-group">
              <NInputNumber
                v-model:value="formData.protein"
                :precision="2"
                :min="0"
                placeholder="含量"
                class="nutrition-value-input"
              >
                <template #suffix>克</template>
              </NInputNumber>
              <NInputNumber
                v-model:value="formData.protein_nrv"
                :precision="2"
                :min="0"
                :max="999"
                placeholder="NRV%"
                class="nutrition-nrv-input"
              >
                <template #suffix>%NRV</template>
              </NInputNumber>
            </div>
          </NFormItemGi>
        </NGrid>

        <!-- 更多营养成分 -->
        <div v-if="!isMobile || showMoreNutrition" class="more-nutrition-section">
          <NDivider v-if="!isMobile" title-placement="left">详细营养成分</NDivider>

          <NGrid :cols="isMobile ? 1 : 2" :x-gap="16" :y-gap="16" responsive="screen">
            <!-- 饱和脂肪 -->
            <NFormItemGi>
              <template #label>
                <span>饱和脂肪</span>
                <NText depth="3" style="font-size: 12px; margin-left: 8px;">(基于20g)</NText>
              </template>
              <div class="nutrition-input-group">
                <NInputNumber
                  v-model:value="formData.saturated_fat"
                  :precision="2"
                  :min="0"
                  placeholder="含量"
                  class="nutrition-value-input"
                >
                  <template #suffix>克</template>
                </NInputNumber>
                <NInputNumber
                  v-model:value="formData.saturated_fat_nrv"
                  :precision="2"
                  :min="0"
                  :max="999"
                  placeholder="NRV%"
                  class="nutrition-nrv-input"
                >
                  <template #suffix>%NRV</template>
                </NInputNumber>
              </div>
            </NFormItemGi>

            <!-- 反式脂肪 -->
            <NFormItemGi label="反式脂肪">
              <div class="nutrition-input-group">
                <NInputNumber
                  v-model:value="formData.trans_fat"
                  :precision="2"
                  :min="0"
                  placeholder="含量"
                  class="nutrition-value-input"
                >
                  <template #suffix>克</template>
                </NInputNumber>
                <div class="nutrition-nrv-placeholder">无NRV参考值</div>
              </div>
            </NFormItemGi>

            <!-- 胆固醇 -->
            <NFormItemGi>
              <template #label>
                <span>胆固醇</span>
                <NText depth="3" style="font-size: 12px; margin-left: 8px;">(基于300mg)</NText>
              </template>
               <div class="nutrition-input-group">
                  <NInputNumber
                  v-model:value="formData.cholesterol"
                  :precision="2"
                  :min="0"
                  placeholder="含量"
                  class="nutrition-value-input"
                >
                  <template #suffix>毫克</template>
                </NInputNumber>
                <NInputNumber
                  v-model:value="formData.cholesterol_nrv"
                  :precision="2"
                  :min="0"
                  :max="999"
                  placeholder="NRV%"
                  class="nutrition-nrv-input"
                >
                  <template #suffix>%NRV</template>
                </NInputNumber>
               </div>
            </NFormItemGi>

            <!-- 钠 -->
            <NFormItemGi>
              <template #label>
                <span>钠</span>
                <NText depth="3" style="font-size: 12px; margin-left: 8px;">(基于2000mg)</NText>
              </template>
              <div class="nutrition-input-group">
                <NInputNumber
                  v-model:value="formData.sodium"
                  :precision="2"
                  :min="0"
                  placeholder="含量"
                  class="nutrition-value-input"
                >
                  <template #suffix>毫克</template>
                </NInputNumber>
                <NInputNumber
                  v-model:value="formData.sodium_nrv"
                  :precision="2"
                  :min="0"
                  :max="999"
                  placeholder="NRV%"
                  class="nutrition-nrv-input"
                >
                  <template #suffix>%NRV</template>
                </NInputNumber>
              </div>
            </NFormItemGi>

            <!-- 钾 -->
            <NFormItemGi>
              <template #label>
                <span>钾</span>
                <NText depth="3" style="font-size: 12px; margin-left: 8px;">(基于3500mg)</NText>
              </template>
              <div class="nutrition-input-group">
                <NInputNumber
                  v-model:value="formData.potassium"
                  :precision="2"
                  :min="0"
                  placeholder="含量"
                  class="nutrition-value-input"
                >
                  <template #suffix>毫克</template>
                </NInputNumber>
                <NInputNumber
                  v-model:value="formData.potassium_nrv"
                  :precision="2"
                  :min="0"
                  :max="999"
                  placeholder="NRV%"
                  class="nutrition-nrv-input"
                >
                  <template #suffix>%NRV</template>
                </NInputNumber>
              </div>
            </NFormItemGi>

            <!-- 膳食纤维 -->
            <NFormItemGi>
              <template #label>
                <span>膳食纤维</span>
                <NText depth="3" style="font-size: 12px; margin-left: 8px;">(基于25g)</NText>
              </template>
              <div class="nutrition-input-group">
                <NInputNumber
                  v-model:value="formData.dietary_fiber"
                  :precision="2"
                  :min="0"
                  placeholder="含量"
                  class="nutrition-value-input"
                >
                  <template #suffix>克</template>
                </NInputNumber>
                <NInputNumber
                  v-model:value="formData.dietary_fiber_nrv"
                  :precision="2"
                  :min="0"
                  :max="999"
                  placeholder="NRV%"
                  class="nutrition-nrv-input"
                >
                  <template #suffix>%NRV</template>
                </NInputNumber>
              </div>
            </NFormItemGi>

            <!-- 糖类 -->
            <NFormItemGi label="糖类">
              <div class="nutrition-input-group">
                <NInputNumber
                  v-model:value="formData.sugar"
                  :precision="2"
                  :min="0"
                  placeholder="含量"
                  class="nutrition-value-input"
                >
                  <template #suffix>克</template>
                </NInputNumber>
                <div class="nutrition-nrv-placeholder">无NRV参考值</div>
              </div>
            </NFormItemGi>
          </NGrid>
        </div>

        <!-- 维生素和矿物质 -->
        <div v-if="!isMobile || showMoreNutrition" class="vitamins-section">
          <NDivider title-placement="left">维生素和矿物质</NDivider>

          <NGrid :cols="isMobile ? 1 : 2" :x-gap="16" :y-gap="16">
            <!-- 维生素A -->
            <NFormItemGi>
              <template #label>
                <span>维生素A</span>
                <NText depth="3" style="font-size: 12px; margin-left: 8px;">(基于800μg)</NText>
              </template>
              <div class="nutrition-input-group">
                <NInputNumber
                  v-model:value="formData.vitamin_a"
                  :precision="2"
                  :min="0"
                  placeholder="含量"
                  class="nutrition-value-input"
                >
                  <template #suffix>μg</template>
                </NInputNumber>
                <NInputNumber
                  v-model:value="formData.vitamin_a_nrv"
                  :precision="2"
                  :min="0"
                  :max="999"
                  placeholder="NRV%"
                  class="nutrition-nrv-input"
                >
                  <template #suffix>%NRV</template>
                </NInputNumber>
              </div>
            </NFormItemGi>

            <!-- 维生素C -->
            <NFormItemGi>
              <template #label>
                <span>维生素C</span>
                <NText depth="3" style="font-size: 12px; margin-left: 8px;">(基于100mg)</NText>
              </template>
              <div class="nutrition-input-group">
                <NInputNumber
                  v-model:value="formData.vitamin_c"
                  :precision="2"
                  :min="0"
                  placeholder="含量"
                  class="nutrition-value-input"
                >
                  <template #suffix>mg</template>
                </NInputNumber>
                <NInputNumber
                  v-model:value="formData.vitamin_c_nrv"
                  :precision="2"
                  :min="0"
                  :max="999"
                  placeholder="NRV%"
                  class="nutrition-nrv-input"
                >
                  <template #suffix>%NRV</template>
                </NInputNumber>
              </div>
            </NFormItemGi>

            <!-- 钙 -->
            <NFormItemGi>
              <template #label>
                <span>钙</span>
                <NText depth="3" style="font-size: 12px; margin-left: 8px;">(基于800mg)</NText>
              </template>
              <div class="nutrition-input-group">
                <NInputNumber
                  v-model:value="formData.calcium"
                  :precision="2"
                  :min="0"
                  placeholder="含量"
                  class="nutrition-value-input"
                >
                  <template #suffix>mg</template>
                </NInputNumber>
                <NInputNumber
                  v-model:value="formData.calcium_nrv"
                  :precision="2"
                  :min="0"
                  :max="999"
                  placeholder="NRV%"
                  class="nutrition-nrv-input"
                >
                  <template #suffix>%NRV</template>
                </NInputNumber>
              </div>
            </NFormItemGi>

            <!-- 铁 -->
            <NFormItemGi>
              <template #label>
                <span>铁</span>
                <NText depth="3" style="font-size: 12px; margin-left: 8px;">(基于15mg)</NText>
              </template>
              <div class="nutrition-input-group">
                <NInputNumber
                  v-model:value="formData.iron"
                  :precision="2"
                  :min="0"
                  placeholder="含量"
                  class="nutrition-value-input"
                >
                  <template #suffix>mg</template>
                </NInputNumber>
                <NInputNumber
                  v-model:value="formData.iron_nrv"
                  :precision="2"
                  :min="0"
                  :max="999"
                  placeholder="NRV%"
                  class="nutrition-nrv-input"
                >
                  <template #suffix>%NRV</template>
                </NInputNumber>
              </div>
            </NFormItemGi>
          </NGrid>
        </div>

        <!-- 扩展营养成分 -->
        <NDivider title-placement="left">扩展营养成分 (可选)</NDivider>
        <div class="extended-nutrition-section">
          <div class="nutrition-header">
            <NText depth="3">
              添加其他营养成分信息，如维生素、矿物质等。数据将以 JSON 格式保存。
            </NText>
            <NButton
              type="primary"
              size="small"
              @click="addNutritionItem"
              style="margin-top: 8px;"
            >
              <template #icon>
                <NIcon><AddOutline /></NIcon>
              </template>
              添加营养成分
            </NButton>
          </div>

          <div v-if="nutritionItems.length === 0" class="empty-nutrition">
            <NEmpty description="暂无扩展营养成分" size="small" />
          </div>

          <div v-else class="nutrition-items">
            <div
              v-for="(item, index) in nutritionItems"
              :key="index"
              class="nutrition-item"
            >
              <div class="extended-nutrition-row">
                <div class="extended-nutrition-name">
                  <NInput
                    v-model:value="item.name"
                    placeholder="营养成分名称"
                    clearable
                  />
                </div>
                <div class="extended-nutrition-inputs">
                  <div class="nutrition-input-group">
                    <NInputNumber
                      v-model:value="item.nutrition"
                      :precision="2"
                      :min="0"
                      placeholder="含量"
                      class="nutrition-value-input"
                    />
                    <NInput
                      v-model:value="item.unit"
                      placeholder="单位"
                      class="nutrition-unit-input"
                    />
                    <NInputNumber
                      v-model:value="item.nrv"
                      :precision="2"
                      :min="0"
                      :max="999"
                      placeholder="NRV%"
                      class="nutrition-nrv-input"
                    >
                      <template #suffix>%NRV</template>
                    </NInputNumber>
                  </div>
                </div>
                <div class="extended-nutrition-actions">
                  <NButton
                    size="small"
                    quaternary
                    type="error"
                    @click="removeNutritionItem(index)"
                  >
                    <template #icon>
                      <NIcon><TrashOutline /></NIcon>
                    </template>
                  </NButton>
                </div>
              </div>
            </div>
          </div>

          <!-- JSON 预览 -->
          <div v-if="nutritionItems.length > 0" class="json-preview">
            <NText depth="3" style="font-size: 12px;">JSON 预览：</NText>
            <pre class="json-code">{{ nutritionJsonPreview }}</pre>
          </div>
        </div>

        <!-- 其他信息 -->
        <NDivider title-placement="left">其他信息</NDivider>
        <NFormItem label="描述" path="description">
          <NInput
            v-model:value="formData.description"
            type="textarea"
            placeholder="食物描述（可选）"
            :rows="3"
          />
        </NFormItem>

        <NText depth="3" style="font-size: 12px;">
          * 每日摄入量百分比基于 2000 大卡的饮食方案。实际个人需求可能因性别与 BMI 情况而变化。
        </NText>

        <!-- 操作按钮 -->
        <NSpace justify="space-between" style="margin-top: 24px;">
          <NButton @click="handleCancel">取消</NButton>
          <NButton type="primary" @click="handleSave" :loading="loading">保存</NButton>
        </NSpace>
      </NForm>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useResponsive } from '../composables/useResponsive'
import {
  NCard,
  NForm,
  NFormItem,
  NFormItemGi,
  NInput,
  NInputNumber,
  NSelect,
  NButton,
  NSpace,
  NGrid,
  NDivider,
  NText,
  NEmpty,
  NIcon,
  type FormInst,
  type FormRules
} from 'naive-ui'
import { AddOutline, TrashOutline } from '@vicons/ionicons5'
import { useFoodStore } from '../stores/food'
import { calculateNRV, type NutrientKey } from '../utils/nrvCalculator'

const router = useRouter()
const message = useMessage()
const foodStore = useFoodStore()
const { isMobile } = useResponsive()

const formRef = ref<FormInst | null>(null)
const loading = ref(false)
const showMoreNutrition = ref(false)

// 营养成分条目接口
interface NutritionItem {
  name: string
  nutrition: number
  unit: string
  nrv: number
}

// 表单数据
const formData = reactive({
  name: '',
  brand: '',
  barcode: '',
  calories: 0,
  carbohydrates: 0,
  total_fat: 0,
  protein: 0,
  saturated_fat: 0,
  trans_fat: 0,
  cholesterol: 0,
  sodium: 0,
  potassium: 0,
  dietary_fiber: 0,
  sugar: 0,
  vitamin_a: 0,
  vitamin_c: 0,
  calcium: 0,
  iron: 0,
  // NRV% 字段
  calories_nrv: 0,
  carbohydrates_nrv: 0,
  total_fat_nrv: 0,
  protein_nrv: 0,
  saturated_fat_nrv: 0,
  cholesterol_nrv: 0,
  sodium_nrv: 0,
  potassium_nrv: 0,
  dietary_fiber_nrv: 0,
  vitamin_a_nrv: 0,
  vitamin_c_nrv: 0,
  calcium_nrv: 0,
  iron_nrv: 0,
  tagIds: [] as number[],
  description: ''
})

// 扩展营养成分条目
const nutritionItems = ref<NutritionItem[]>([])

// JSON 预览
const nutritionJsonPreview = computed(() => {
  const jsonData: Array<{name: string, nutrition: number, unit: string, nrv: number}> = []
  nutritionItems.value.forEach(item => {
    if (item.name.trim() && item.nutrition > 0) {
      jsonData.push({
        name: item.name.trim(),
        nutrition: item.nutrition,
        unit: item.unit,
        nrv: item.nrv
      })
    }
  })
  return JSON.stringify(jsonData, null, 2)
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入食物名称', trigger: 'blur' }
  ]
}

// 标签选项
const tagOptions = computed(() => {
  return foodStore.tags.map(tag => ({
    label: tag.name,
    value: tag.id
  }))
})

// 取消操作
const handleCancel = () => {
  router.back()
}

// 添加营养成分条目
const addNutritionItem = () => {
  nutritionItems.value.push({
    name: '',
    nutrition: 0,
    unit: 'mg',
    nrv: 0
  })
}

// 移除营养成分条目
const removeNutritionItem = (index: number) => {
  nutritionItems.value.splice(index, 1)
}

// 自动计算NRV%
const calculateNRVForNutrient = (nutrientKey: NutrientKey, value: number) => {
  if (value > 0) {
    const nrvValue = calculateNRV(value, nutrientKey)
    const nrvKey = `${nutrientKey}_nrv` as keyof typeof formData
    if (nrvKey in formData) {
      ;(formData as any)[nrvKey] = nrvValue
    }
  }
}

// 营养素值变化时自动计算NRV%
const handleNutrientChange = (nutrientKey: NutrientKey, value: number) => {
  calculateNRVForNutrient(nutrientKey, value)
}

// 自动计算所有NRV%
const autoCalculateAllNRV = () => {
  const nutrients: Array<{ key: NutrientKey; value: number }> = [
    { key: 'calories', value: formData.calories },
    { key: 'carbohydrates', value: formData.carbohydrates },
    { key: 'total_fat', value: formData.total_fat },
    { key: 'protein', value: formData.protein },
    { key: 'saturated_fat', value: formData.saturated_fat },
    { key: 'cholesterol', value: formData.cholesterol },
    { key: 'sodium', value: formData.sodium },
    { key: 'potassium', value: formData.potassium },
    { key: 'dietary_fiber', value: formData.dietary_fiber },
    { key: 'vitamin_a', value: formData.vitamin_a },
    { key: 'vitamin_c', value: formData.vitamin_c },
    { key: 'calcium', value: formData.calcium },
    { key: 'iron', value: formData.iron },
  ]

  nutrients.forEach(({ key, value }) => {
    if (value > 0) {
      calculateNRVForNutrient(key, value)
    }
  })

  message.success('NRV%计算完成')
}

// 保存食物
const handleSave = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    // 构建扩展营养成分 JSON
    const extendedNutrition: Array<{name: string, nutrition: number, unit: string, nrv: number}> = []
    nutritionItems.value.forEach(item => {
      if (item.name.trim() && item.nutrition > 0) {
        extendedNutrition.push({
          name: item.name.trim(),
          nutrition: item.nutrition,
          unit: item.unit,
          nrv: item.nrv
        })
      }
    })

    // 构建食物数据
    const foodData = {
      ...formData,
      extended_nutrition: Object.keys(extendedNutrition).length > 0 ? extendedNutrition : undefined
    }

    // 这里应该调用API保存食物
    console.log('保存食物数据:', foodData)

    // 暂时模拟保存成功
    await new Promise(resolve => setTimeout(resolve, 1000))

    message.success('食物添加成功')
    router.push('/foods')
  } catch (error) {
    message.error('请检查表单填写是否正确')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 加载标签数据
  // foodStore.loadTags()
})
</script>

<style scoped>
.add-food-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

/* 扩展营养成分样式 */
.extended-nutrition-section {
  margin-top: 16px;
}

.extended-nutrition-row {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 12px;
}

.extended-nutrition-name {
  flex: 2;
  min-width: 0;
}

.extended-nutrition-inputs {
  flex: 5;
  min-width: 0;
}

.extended-nutrition-actions {
  flex: 0 0 auto;
}

.nutrition-header {
  margin-bottom: 16px;
}

.empty-nutrition {
  padding: 20px 0;
  text-align: center;
}

.nutrition-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.nutrition-item {
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #fafafa;
}

.nutrition-item-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.json-preview {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.json-code {
  background-color: #f8f8f8;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
  font-family: 'Fira Code', Consolas, 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #24292e;
  overflow-x: auto;
  white-space: pre;
}

.json-preview .n-code {
  background-color: transparent;
  padding: 0;
}

/* 营养成分输入组样式 */
.nutrition-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.nutrition-value-input {
  flex: 3;
  min-width: 0;
}

.nutrition-nrv-input {
  flex: 2;
  min-width: 0;
}

.nutrition-unit-input {
  flex: 1;
  min-width: 0;
}

.nutrition-nrv-placeholder {
  flex: 1;
  padding: 6px 12px;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  color: #999;
  font-size: 12px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 34px;
}

/* 更多营养成分样式 */
.more-nutrition-section {
  margin-top: 16px;
}

.vitamins-section {
  margin-top: 16px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .nutrition-item {
    padding: 8px;
  }

  .nutrition-items {
    gap: 8px;
  }

  .json-preview {
    padding: 8px;
  }

  /* 移动端营养成分表单项样式 */
  .more-nutrition-section,
  .vitamins-section {
    margin-top: 12px;
  }

  /* 移动端表单项间距调整 */
  .n-form-item {
    margin-bottom: 16px;
  }

  /* 移动端输入框样式 */
  .n-input-number {
    width: 100% !important;
  }

  /* 移动端营养成分输入组 - 保持在同一行 */
  .nutrition-input-group {
    flex-direction: row;
    gap: 6px;
  }

  .nutrition-value-input {
    flex: 2;
    min-width: 0;
  }

  .nutrition-nrv-input {
    flex: 1.5;
    min-width: 0;
  }

  .nutrition-unit-input {
    flex: 1;
    min-width: 0;
  }

  .nutrition-nrv-placeholder {
    flex: 1.5;
    min-width: 0;
    font-size: 11px;
    padding: 4px 8px;
  }

  /* 移动端扩展营养成分布局 */
  .extended-nutrition-row {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .extended-nutrition-name,
  .extended-nutrition-inputs {
    flex: none;
    width: 100%;
  }

  .extended-nutrition-actions {
    align-self: flex-end;
  }
}
</style>
