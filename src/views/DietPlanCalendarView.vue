<template>
  <div class="diet-plan-calendar-view">
    <div class="page-header">
      <h1>饮食计划日历</h1>
      <div class="header-actions">
        <NButtonGroup>
          <NButton
            :type="viewMode === 'calendar' ? 'primary' : 'default'"
            @click="viewMode = 'calendar'"
          >
            <template #icon>
              <NIcon><CalendarOutline /></NIcon>
            </template>
            日历视图
          </NButton>
          <NButton
            :type="viewMode === 'timeline' ? 'primary' : 'default'"
            @click="viewMode = 'timeline'"
          >
            <template #icon>
              <NIcon><TimeOutline /></NIcon>
            </template>
            时间线视图
          </NButton>
        </NButtonGroup>
        
        <NButton type="primary" @click="goToCreatePlan">
          <template #icon>
            <NIcon><AddOutline /></NIcon>
          </template>
          创建计划
        </NButton>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <NCard class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ totalPlans }}</div>
          <div class="stat-label">总计划数</div>
        </div>
        <div class="stat-icon">
          <NIcon size="24" color="#1890ff">
            <DocumentTextOutline />
          </NIcon>
        </div>
      </NCard>

      <NCard class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ completedPlans }}</div>
          <div class="stat-label">已完成</div>
        </div>
        <div class="stat-icon">
          <NIcon size="24" color="#52c41a">
            <CheckmarkCircleOutline />
          </NIcon>
        </div>
      </NCard>

      <NCard class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ averageCompletion }}%</div>
          <div class="stat-label">平均完成度</div>
        </div>
        <div class="stat-icon">
          <NIcon size="24" color="#faad14">
            <StatsChartOutline />
          </NIcon>
        </div>
      </NCard>

      <NCard class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ currentStreak }}</div>
          <div class="stat-label">连续天数</div>
        </div>
        <div class="stat-icon">
          <NIcon size="24" color="#722ed1">
            <FlameOutline />
          </NIcon>
        </div>
      </NCard>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 日历视图 -->
      <div v-if="viewMode === 'calendar'" class="calendar-view">
        <div class="calendar-section">
          <DietPlanCalendar @date-selected="handleDateSelected" />
        </div>

        <!-- 当天计划列表 -->
        <div class="daily-plan-section">
          <NCard :title="selectedDateTitle" class="daily-plan-card">
            <div v-if="selectedDatePlan" class="plan-content">
              <div class="plan-info">
                <div class="plan-header">
                  <h4>{{ selectedDatePlan.planName || '饮食计划' }}</h4>
                  <NTag
                    :color="{ color: getDietTypeColor(selectedDatePlan.dietTypeId) }"
                    size="small"
                  >
                    {{ getDietTypeName(selectedDatePlan.dietTypeId) }}
                  </NTag>
                </div>
                <div class="completion-summary">
                  <NProgress
                    :percentage="selectedDateCompletionRate"
                    :show-indicator="false"
                    :height="6"
                  />
                  <span class="completion-text">
                    {{ getCompletedItemsCount(selectedDatePlan.id) }} / {{ getTotalItemsCount(selectedDatePlan.id) }} 项已完成 ({{ selectedDateCompletionRate }}%)
                  </span>
                </div>
              </div>

              <div class="plan-items">
                <h5>食物分类清单</h5>
                <div class="items-list">
                  <div
                    v-for="item in selectedDateItems"
                    :key="item.id"
                    class="plan-item"
                    :class="{ completed: item.isConsumed }"
                  >
                    <div class="item-checkbox">
                      <NCheckbox
                        :checked="item.isConsumed"
                        size="large"
                        @update:checked="(checked) => toggleItemConsumption(item.id, checked)"
                      />
                    </div>

                    <div class="item-content">
                      <div class="item-header">
                        <span class="item-name">{{ item.categoryName }}</span>
                        <NTag
                          :type="item.isConsumed ? 'success' : 'default'"
                          size="small"
                        >
                          {{ item.isConsumed ? '已完成' : '待完成' }}
                        </NTag>
                      </div>

                      <p v-if="item.categoryDescription" class="item-description">
                        {{ item.categoryDescription }}
                      </p>

                      <div v-if="item.specificFoods && item.specificFoods.length > 0" class="specific-foods">
                        <div
                          v-for="food in item.specificFoods"
                          :key="food.id"
                          class="food-item"
                        >
                          <span class="food-name">{{ food.name }}</span>
                          <span v-if="food.amount && food.unit" class="food-amount">
                            {{ food.amount }}{{ food.unit }}
                          </span>
                        </div>
                      </div>

                      <div v-if="item.isConsumed && item.consumedAt" class="consumed-time">
                        完成时间: {{ formatTime(item.consumedAt) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="selectedDatePlan.notes" class="plan-notes">
                <h5>备注</h5>
                <p>{{ selectedDatePlan.notes }}</p>
              </div>
            </div>

            <div v-else class="no-plan">
              <NEmpty description="当天没有饮食计划">
                <template #extra>
                  <NButton type="primary" @click="createPlanForSelectedDate">
                    为这一天创建计划
                  </NButton>
                </template>
              </NEmpty>
            </div>
          </NCard>
        </div>
      </div>

      <!-- 时间线视图 -->
      <div v-else class="timeline-container">
        <NCard title="饮食计划时间线">
          <div class="timeline">
            <div
              v-for="plan in sortedPlans"
              :key="plan.id"
              class="timeline-item"
              :class="{ completed: isFullyCompleted(plan.id) }"
            >
              <div class="timeline-date">
                <div class="date-day">{{ formatDay(plan.planDate) }}</div>
                <div class="date-month">{{ formatMonth(plan.planDate) }}</div>
              </div>
              
              <div class="timeline-content">
                <div class="plan-header">
                  <h4>{{ plan.planName || '饮食计划' }}</h4>
                  <div class="plan-badges">
                    <NTag
                      :color="{ color: getDietTypeColor(plan.dietTypeId) }"
                      size="small"
                    >
                      {{ getDietTypeName(plan.dietTypeId) }}
                    </NTag>
                    <NTag
                      :type="getCompletionTagType(plan.id)"
                      size="small"
                    >
                      {{ getCompletionRate(plan.id) }}%
                    </NTag>
                  </div>
                </div>
                
                <div class="plan-progress">
                  <NProgress
                    :percentage="getCompletionRate(plan.id)"
                    :show-indicator="false"
                    :height="6"
                    :border-radius="3"
                  />
                </div>
                
                <div class="plan-items-summary">
                  <span>{{ getCompletedItemsCount(plan.id) }} / {{ getTotalItemsCount(plan.id) }} 项已完成</span>
                </div>
                
                <div class="plan-actions">
                  <NButton size="small" @click="viewPlanDetail(plan)">
                    查看详情
                  </NButton>
                  <NButton size="small" type="primary" @click="goToTracker(plan.planDate)">
                    开始跟踪
                  </NButton>
                </div>
              </div>
            </div>
          </div>
          
          <div v-if="sortedPlans.length === 0" class="empty-timeline">
            <NEmpty description="还没有任何饮食计划">
              <template #extra>
                <NButton type="primary" @click="goToCreatePlan">
                  创建第一个计划
                </NButton>
              </template>
            </NEmpty>
          </div>
        </NCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  NCard,
  NButton,
  NButtonGroup,
  NIcon,
  NTag,
  NProgress,
  NEmpty,
  NCheckbox,
  useMessage
} from 'naive-ui'
import {
  CalendarOutline,
  TimeOutline,
  AddOutline,
  DocumentTextOutline,
  CheckmarkCircleOutline,
  StatsChartOutline,
  FlameOutline
} from '@vicons/ionicons5'
import { useRouter } from 'vue-router'
import { useDailyDietPlanStore } from '../stores/dailyDietPlan'
import { useDietTypeStore } from '../stores/dietType'
import DietPlanCalendar from '../components/DietPlanCalendar.vue'

const router = useRouter()
const dailyPlanStore = useDailyDietPlanStore()
const dietTypeStore = useDietTypeStore()
const message = useMessage()

// 响应式状态
const viewMode = ref<'calendar' | 'timeline'>('calendar')
const selectedDate = ref<Date>(new Date()) // 默认选中今天

// 计算属性
const totalPlans = computed(() => dailyPlanStore.dailyPlans.length)

const completedPlans = computed(() => {
  return dailyPlanStore.dailyPlans.filter(plan => 
    getCompletionRate(plan.id) === 100
  ).length
})

const averageCompletion = computed(() => {
  if (totalPlans.value === 0) return 0
  const total = dailyPlanStore.dailyPlans.reduce((sum, plan) => 
    sum + getCompletionRate(plan.id), 0
  )
  return Math.round(total / totalPlans.value)
})

const currentStreak = computed(() => {
  // 计算连续完成天数
  const sortedPlans = [...dailyPlanStore.dailyPlans]
    .sort((a, b) => new Date(b.planDate).getTime() - new Date(a.planDate).getTime())
  
  let streak = 0
  for (const plan of sortedPlans) {
    if (getCompletionRate(plan.id) === 100) {
      streak++
    } else {
      break
    }
  }
  return streak
})

const sortedPlans = computed(() => {
  return [...dailyPlanStore.dailyPlans]
    .sort((a, b) => new Date(b.planDate).getTime() - new Date(a.planDate).getTime())
})

// 新增的计算属性
const selectedDateString = computed(() => {
  return selectedDate.value.toISOString().split('T')[0]
})

const selectedDateTitle = computed(() => {
  const date = selectedDate.value
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 - 饮食计划`
})

const selectedDatePlan = computed(() => {
  return dailyPlanStore.getPlanByDate(selectedDateString.value)
})

const selectedDateItems = computed(() => {
  if (!selectedDatePlan.value) return []
  return dailyPlanStore.getPlanItemsByPlanId(selectedDatePlan.value.id)
})

const selectedDateCompletionRate = computed(() => {
  if (!selectedDatePlan.value) return 0
  return getCompletionRate(selectedDatePlan.value.id)
})

// 方法
const getCompletionRate = (planId: number): number => {
  const items = dailyPlanStore.getPlanItemsByPlanId(planId)
  if (items.length === 0) return 0
  
  const completedItems = items.filter(item => item.isConsumed).length
  return Math.round((completedItems / items.length) * 100)
}

const isFullyCompleted = (planId: number): boolean => {
  return getCompletionRate(planId) === 100
}

const getCompletedItemsCount = (planId: number): number => {
  const items = dailyPlanStore.getPlanItemsByPlanId(planId)
  return items.filter(item => item.isConsumed).length
}

const getTotalItemsCount = (planId: number): number => {
  return dailyPlanStore.getPlanItemsByPlanId(planId).length
}

const getDietTypeName = (dietTypeId?: number): string => {
  if (!dietTypeId) return '自定义计划'
  const dietType = dietTypeStore.dietTypes.find(dt => dt.id === dietTypeId)
  return dietType?.name || '未知类型'
}

const getDietTypeColor = (dietTypeId?: number): string => {
  if (!dietTypeId) return '#d9d9d9'
  const dietType = dietTypeStore.dietTypes.find(dt => dt.id === dietTypeId)
  return dietType?.color || '#1890ff'
}

const getCompletionTagType = (planId: number) => {
  const rate = getCompletionRate(planId)
  if (rate === 100) return 'success'
  if (rate >= 50) return 'warning'
  return 'default'
}

const formatDay = (dateString: string): string => {
  return new Date(dateString).getDate().toString()
}

const formatMonth = (dateString: string): string => {
  const date = new Date(dateString)
  return `${date.getMonth() + 1}月`
}

const goToCreatePlan = () => {
  router.push({ name: 'DailyDietPlan' })
}

const goToTracker = (planDate: string) => {
  router.push({ 
    name: 'DailyDietTracker',
    query: { date: planDate }
  })
}

const viewPlanDetail = (plan: any) => {
  router.push({
    name: 'DailyDietPlan',
    query: { date: plan.planDate }
  })
}

// 新增的方法
const handleDateSelected = (date: Date) => {
  selectedDate.value = date
}

const toggleItemConsumption = async (itemId: number, consumed: boolean) => {
  try {
    await dailyPlanStore.toggleItemConsumption(itemId, consumed)
    message.success(consumed ? '已标记为完成' : '已取消完成标记')
  } catch (error) {
    message.error('操作失败')
  }
}

const formatTime = (timeString: string): string => {
  return new Date(timeString).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const createPlanForSelectedDate = () => {
  router.push({
    name: 'DailyDietPlan',
    query: { date: selectedDateString.value }
  })
}

// 生命周期
onMounted(async () => {
  await dietTypeStore.fetchDietTypes()
  await dailyPlanStore.fetchDailyPlans(1) // 假设用户ID为1
  
  // 获取所有计划的详细项目
  for (const plan of dailyPlanStore.dailyPlans) {
    await dailyPlanStore.fetchPlanItems(plan.id)
  }
})
</script>

<style scoped>
.diet-plan-calendar-view {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 4px;
}

.page-header h1 {
  margin: 0;
  color: #262626;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  cursor: default;
}

.stat-card :deep(.n-card__content) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
}

.stat-icon {
  opacity: 0.8;
}

.main-content {
  margin-top: 24px;
}

/* 日历视图布局 */
.calendar-view {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 24px;
  align-items: start;
}



.daily-plan-card {
  position: sticky;
  top: 20px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.plan-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.plan-info {
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.plan-header h4 {
  margin: 0;
  color: #262626;
  font-size: 16px;
}

.completion-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.completion-text {
  font-size: 14px;
  color: #666;
  text-align: right;
}

.plan-items h5 {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.plan-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.2s;
}

.plan-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.plan-item.completed {
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.item-checkbox {
  flex-shrink: 0;
  padding-top: 2px;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-name {
  font-weight: 500;
  color: #262626;
  font-size: 15px;
}

.item-description {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 13px;
  line-height: 1.5;
}

.specific-foods {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 8px;
}

.food-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  background-color: #fafafa;
  border-radius: 4px;
  font-size: 13px;
}

.food-name {
  color: #262626;
}

.food-amount {
  color: #666;
  font-weight: 500;
}

.consumed-time {
  font-size: 12px;
  color: #52c41a;
  margin-top: 8px;
}

.plan-notes {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.plan-notes h5 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.plan-notes p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.no-plan {
  text-align: center;
  padding: 40px 20px;
}

.timeline {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.timeline-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.2s;
}

.timeline-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.timeline-item.completed {
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.timeline-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  padding: 8px;
  background-color: #fafafa;
  border-radius: 6px;
}

.date-day {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  line-height: 1;
}

.date-month {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

.timeline-content {
  flex: 1;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.plan-header h4 {
  margin: 0;
  color: #262626;
  font-size: 16px;
}

.plan-badges {
  display: flex;
  gap: 8px;
}

.plan-progress {
  margin-bottom: 8px;
}

.plan-items-summary {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.plan-actions {
  display: flex;
  gap: 8px;
}

.empty-timeline {
  text-align: center;
  padding: 40px 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .calendar-view {
    grid-template-columns: 1fr 350px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: space-between;
  }

  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .calendar-view {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .daily-plan-card {
    position: static;
    max-height: none;
  }

  .plan-item {
    padding: 12px;
  }

  .item-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .timeline-item {
    flex-direction: column;
    gap: 12px;
  }

  .timeline-date {
    align-self: flex-start;
    min-width: auto;
  }

  .plan-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
}
</style>
