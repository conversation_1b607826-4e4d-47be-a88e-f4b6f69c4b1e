<template>
  <div class="records-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <NText tag="h2" class="page-title">营养记录</NText>
      <NButton type="primary" @click="addRecord" class="add-button">
        <template #icon>
          <NIcon><AddOutline /></NIcon>
        </template>
        <span class="add-button-text">添加记录</span>
      </NButton>
    </div>

    <!-- 日期范围选择 -->
    <NCard :bordered="false" style="margin-bottom: 16px;">
      <div class="date-filter">
        <NDatePicker
          v-model:value="dateRange"
          type="daterange"
          clearable
          @update:value="handleDateRangeChange"
          class="date-picker"
        />
      </div>
    </NCard>

    <!-- 记录列表 -->
    <NCard :bordered="false" class="record-list-container">
      <!-- 桌面端：数据表格 -->
      <NDataTable
        v-if="!isMobile"
        :columns="columns"
        :data="filteredRecords"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: any) => row.id"
        class="desktop-table"
      />

      <!-- 移动端：卡片列表 -->
      <MobileRecordList
        v-else
        :records="paginatedRecords"
        @add-record="addRecord"
        @view-record="viewRecord"
        @edit-record="editRecord"
        @delete-record="handleDelete"
      />

      <!-- 移动端分页 -->
      <div v-if="isMobile && filteredRecords.length > 0" class="mobile-pagination">
        <NPagination
          v-model:page="currentPage"
          :page-count="totalPages"
          :page-size="pageSize"
          show-size-picker
          :page-sizes="[10, 20, 50]"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useResponsive } from '../composables/useResponsive'
import MobileRecordList from '../components/mobile/MobileRecordList.vue'
import dayjs from 'dayjs'
import {
  NCard,
  NSpace,
  NText,
  NButton,
  NIcon,
  NDatePicker,
  NDataTable,
  NPopconfirm,
  type DataTableColumns
} from 'naive-ui'
import {
  AddOutline,
  CreateOutline,
  TrashOutline
} from '@vicons/ionicons5'
import { useNutritionStore, type NutritionRecord } from '../stores/nutrition'

const router = useRouter()
const message = useMessage()
const nutritionStore = useNutritionStore()
const { isMobile } = useResponsive()

const loading = ref(false)
const dateRange = ref<[number, number] | null>(null)

// 移动端分页状态
const currentPage = ref(1)
const pageSize = ref(20)

const pagination = {
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true
}

const filteredRecords = computed(() => {
  let records = nutritionStore.records

  // 按日期范围筛选
  if (dateRange.value) {
    const [start, end] = dateRange.value
    const startDate = dayjs(start).format('YYYY-MM-DD')
    const endDate = dayjs(end).format('YYYY-MM-DD')
    records = records.filter(record => 
      record.record_date >= startDate && record.record_date <= endDate
    )
  }

  // 移除餐次筛选，因为不再使用餐次分类

  return records.sort((a, b) => b.record_date.localeCompare(a.record_date))
})

// 移动端分页相关计算属性
const totalPages = computed(() => {
  return Math.ceil(filteredRecords.value.length / pageSize.value)
})

const paginatedRecords = computed(() => {
  if (!isMobile.value) return filteredRecords.value

  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredRecords.value.slice(start, end)
})

const columns: DataTableColumns<NutritionRecord> = [
  {
    title: '日期',
    key: 'record_date',
    width: 120,
    render: (row) => dayjs(row.record_date).format('MM-DD')
  },
  {
    title: '时间',
    key: 'record_time',
    width: 80,
    render: (row) => row.record_time
  },
  {
    title: '食物',
    key: 'food',
    width: 200,
    render: (row) => row.food?.name || row.food_name || '未知食物'
  },
  {
    title: '数量',
    key: 'quantity',
    width: 100,
    render: (row) => `${row.quantity}${row.unit}`
  },
  {
    title: '热量',
    key: 'actual_calories',
    width: 100,
    render: (row) => `${row.actual_calories}千卡`
  },
  {
    title: '蛋白质',
    key: 'actual_protein',
    width: 100,
    render: (row) => `${row.actual_protein}g`
  },
  {
    title: '碳水',
    key: 'actual_carbohydrates',
    width: 100,
    render: (row) => `${row.actual_carbohydrates}g`
  },
  {
    title: '脂肪',
    key: 'actual_total_fat',
    width: 100,
    render: (row) => `${row.actual_total_fat}g`
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    render: (row) => {
      return h(NSpace, { size: 'small' }, {
        default: () => [
          h(NButton, 
            { 
              size: 'small',
              quaternary: true,
              onClick: () => editRecord(row.id)
            },
            { 
              default: () => '编辑',
              icon: () => h(NIcon, null, { default: () => h(CreateOutline) })
            }
          ),
          h(NPopconfirm, 
            {
              onPositiveClick: () => deleteRecord(row.id)
            },
            {
              default: () => '确定删除这条记录吗？',
              trigger: () => h(NButton, 
                { 
                  size: 'small',
                  quaternary: true,
                  type: 'error'
                },
                { 
                  default: () => '删除',
                  icon: () => h(NIcon, null, { default: () => h(TrashOutline) })
                }
              )
            }
          )
        ]
      })
    }
  }
]

const handleDateRangeChange = () => {
  // 日期范围变化时重新加载数据
}

const addRecord = () => {
  router.push('/records/add')
}

const editRecord = (record: NutritionRecord | number) => {
  const id = typeof record === 'number' ? record : record.id
  router.push(`/records/${id}/edit`)
}

const deleteRecord = async (id: number) => {
  try {
    nutritionStore.deleteRecord(id)
    message.success('记录删除成功')
  } catch (error) {
    message.error('删除失败')
  }
}

// 移动端专用方法
const viewRecord = (record: NutritionRecord) => {
  // 可以显示记录详情弹窗或跳转到详情页
  message.info(`查看记录：${record.food?.name || record.food_name}`)
}

const handleDelete = async (record: NutritionRecord) => {
  await deleteRecord(record.id)
}

const handlePageSizeChange = (newPageSize: number) => {
  pageSize.value = newPageSize
  currentPage.value = 1 // 重置到第一页
}

onMounted(() => {
  // 加载记录数据
})
</script>

<style scoped>
.records-page {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;
}

.page-title {
  margin: 0;
  flex: 1;
}

.add-button {
  flex-shrink: 0;
}

.add-button-text {
  margin-left: 4px;
}

/* 日期筛选样式 */
.date-filter {
  display: flex;
  justify-content: flex-start;
}

.date-picker {
  max-width: 300px;
}

/* 记录列表容器 */
.record-list-container {
  width: 100%;
}

.desktop-table {
  width: 100%;
}

.mobile-pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 移动端适配 */
@media (max-width: 768px) {

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .page-title {
    text-align: center;
    font-size: 18px;
  }

  .add-button {
    width: 100%;
  }

  .date-filter {
    justify-content: center;
  }

  .date-picker {
    max-width: 100%;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .page-header {
    gap: 8px;
  }

  .page-title {
    font-size: 16px;
  }

  .add-button-text {
    display: none;
  }
}
</style>
