<template>
  <div class="dashboard">
    <!-- 日期选择器 -->
    <NCard :bordered="false" style="margin-bottom: 16px;">
      <div class="date-selector" :class="{ 'mobile-layout': isMobile }">
        <div class="date-label">
          <NText strong>选择日期：</NText>
        </div>
        <div class="date-controls" :class="{ 'mobile-controls': isMobile }">
          <NDatePicker
            v-model:value="selectedDate"
            type="date"
            clearable
            @update:value="handleDateChange"
            class="date-picker"
            :class="{ 'mobile-picker': isMobile }"
          />
          <NButton @click="goToToday" class="today-button" :class="{ 'mobile-today-btn': isMobile }">
            今天
          </NButton>
        </div>
      </div>
    </NCard>

    <!-- 营养摄入概览 -->
    <div class="nutrition-overview">
      <NGrid
        :cols="getGridCols({ xs: 2, sm: 2, md: 4, lg: 4, xl: 4 })"
        :x-gap="getSpacing({ xs: 8, sm: 12, md: 16, lg: 20, xl: 24 })"
        :y-gap="getSpacing({ xs: 8, sm: 12, md: 16, lg: 20, xl: 24 })"
        responsive="screen"
        style="margin-bottom: 24px;"
      >
        <NGridItem>
          <NCard class="nutrition-card">
            <div class="nutrition-item">
              <div class="nutrition-icon">
                <NIcon color="#f56565" size="24">
                  <FlameOutline />
                </NIcon>
              </div>
              <div class="nutrition-content">
                <div class="nutrition-label">总热量</div>
                <div class="nutrition-value">
                  {{ dailySummary?.total_calories || 0 }}
                  <span class="nutrition-unit">千卡</span>
                </div>
              </div>
            </div>
          </NCard>
        </NGridItem>
        <NGridItem>
          <NCard class="nutrition-card">
            <div class="nutrition-item">
              <div class="nutrition-icon">
                <NIcon color="#ed8936" size="24">
                  <NutritionOutline />
                </NIcon>
              </div>
              <div class="nutrition-content">
                <div class="nutrition-label">碳水化合物</div>
                <div class="nutrition-value">
                  {{ dailySummary?.total_carbohydrates || 0 }}
                  <span class="nutrition-unit">g</span>
                </div>
              </div>
            </div>
          </NCard>
        </NGridItem>
        <NGridItem>
          <NCard class="nutrition-card">
            <div class="nutrition-item">
              <div class="nutrition-icon">
                <NIcon color="#38b2ac" size="24">
                  <FitnessOutline />
                </NIcon>
              </div>
              <div class="nutrition-content">
                <div class="nutrition-label">蛋白质</div>
                <div class="nutrition-value">
                  {{ dailySummary?.total_protein || 0 }}
                  <span class="nutrition-unit">g</span>
                </div>
              </div>
            </div>
          </NCard>
        </NGridItem>
        <NGridItem>
          <NCard class="nutrition-card">
            <div class="nutrition-item">
              <div class="nutrition-icon">
                <NIcon color="#9f7aea" size="24">
                  <WaterOutline />
                </NIcon>
              </div>
              <div class="nutrition-content">
                <div class="nutrition-label">脂肪</div>
                <div class="nutrition-value">
                  {{ dailySummary?.total_fat || 0 }}
                  <span class="nutrition-unit">g</span>
                </div>
              </div>
            </div>
          </NCard>
        </NGridItem>
      </NGrid>
    </div>

    <!-- 主要内容区域 -->
    <NGrid
      :cols="getGridCols({ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 })"
      :x-gap="getSpacing({ xs: 0, sm: 0, md: 16, lg: 20, xl: 24 })"
      :y-gap="getSpacing({ xs: 16, sm: 16, md: 16, lg: 20, xl: 24 })"
      responsive="screen"
    >
      <!-- 今日饮食跟踪 -->
      <NGridItem>
        <DashboardDietTracker />
      </NGridItem>

      <!-- 今日摄入记录 -->
      <NGridItem>
        <NCard title="今日摄入记录" :bordered="false">
          <template #header-extra>
            <NButton
              size="small"
              @click="addRecord"
            >
              添加记录
            </NButton>
          </template>

          <div v-if="todayRecords.length === 0" class="empty-records">
            <NEmpty description="暂无记录" />
          </div>

          <div v-else>
            <div
              v-for="record in todayRecords"
              :key="record.id"
              class="nutrition-record"
            >
              <NSpace justify="space-between" align="center">
                <div>
                  <NSpace align="center">
                    <NText depth="3" style="font-size: 12px;">{{ record.record_time }}</NText>
                    <NText strong>{{ record.food?.name || record.food_name }}</NText>
                    <NText depth="3">
                      {{ record.quantity }}{{ record.unit }}
                    </NText>
                  </NSpace>
                </div>
                <div>
                  <NSpace align="center">
                    <NText>{{ record.actual_calories }}千卡</NText>
                    <NButton
                      size="small"
                      quaternary
                      type="error"
                      @click="deleteRecord(record.id)"
                    >
                      删除
                    </NButton>
                  </NSpace>
                </div>
              </NSpace>
            </div>
          </div>
        </NCard>
      </NGridItem>
    </NGrid>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { useMessage } from 'naive-ui'
import { useResponsive } from '../composables/useResponsive'
import {
  FlameOutline,
  NutritionOutline,
  FitnessOutline,
  WaterOutline
} from '@vicons/ionicons5'
import { useNutritionStore } from '../stores/nutrition'
import DashboardDietTracker from '../components/DashboardDietTracker.vue'

const router = useRouter()
const message = useMessage()
const nutritionStore = useNutritionStore()
const { isMobile, getGridCols, getSpacing } = useResponsive()

const selectedDate = ref<number>(Date.now())

// 当前日期的营养汇总
const dailySummary = computed(() => {
  try {
    const dateValue = selectedDate.value || Date.now()
    const dateStr = dayjs(dateValue).format('YYYY-MM-DD')
    return nutritionStore.getDailySummary(dateStr)
  } catch (error) {
    console.error('获取营养汇总时日期错误:', error)
    return null
  }
})

// 获取今日记录
const todayRecords = computed(() => {
  try {
    const dateValue = selectedDate.value || Date.now()
    const dateStr = dayjs(dateValue).format('YYYY-MM-DD')
    return nutritionStore.getRecordsByDate(dateStr).sort((a, b) => {
      // 按时间排序
      return a.record_time.localeCompare(b.record_time)
    })
  } catch (error) {
    console.error('获取今日记录时日期错误:', error)
    return []
  }
})

// 日期变化处理
const handleDateChange = (value: number | null) => {
  try {
    if (value && !isNaN(value)) {
      selectedDate.value = value
      loadDayData()
    } else {
      // 如果日期无效，重置为今天
      selectedDate.value = Date.now()
      loadDayData()
    }
  } catch (error) {
    console.error('日期变化处理错误:', error)
    selectedDate.value = Date.now()
    message.error('日期格式错误，已重置为今天')
  }
}

// 跳转到今天
const goToToday = () => {
  selectedDate.value = Date.now()
  loadDayData()
}

// 添加记录
const addRecord = () => {
  try {
    // 确保有有效的日期值
    const dateValue = selectedDate.value || Date.now()
    const dateStr = dayjs(dateValue).format('YYYY-MM-DD')
    const currentTime = dayjs().format('HH:mm')

    router.push({
      name: 'AddRecord',
      query: {
        date: dateStr,
        time: currentTime
      }
    })
  } catch (error) {
    console.error('日期处理错误:', error)
    message.error('日期格式错误，请重新选择日期')
  }
}

// 删除记录
const deleteRecord = async (recordId: number) => {
  try {
    // 这里应该调用API删除记录
    nutritionStore.deleteRecord(recordId)
    message.success('记录删除成功')
    loadDayData()
  } catch (error) {
    message.error('删除失败')
  }
}

// 加载当天数据
const loadDayData = async () => {
  try {
    const dateValue = selectedDate.value || Date.now()
    const dateStr = dayjs(dateValue).format('YYYY-MM-DD')
    // 这里应该调用API加载数据
    // await nutritionStore.loadRecordsByDate(dateStr)
    // await nutritionStore.loadDailySummary(dateStr)
    console.log('加载日期数据:', dateStr)
  } catch (error) {
    console.error('加载数据时日期错误:', error)
  }
}

onMounted(() => {
  loadDayData()
})
</script>

<style scoped>
.dashboard {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0;
}

/* 日期选择器样式 */
.date-selector {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.date-selector.mobile-layout {
  flex-direction: column;
  align-items: stretch;
  gap: 12px;
}

.date-label {
  flex-shrink: 0;
}

.date-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.date-controls.mobile-controls {
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.date-picker {
  flex: 1;
  min-width: 200px;
}

.date-picker.mobile-picker {
  flex: 1;
  min-width: 0;
}

.today-button {
  flex-shrink: 0;
  white-space: nowrap;
}

.today-button.mobile-today-btn {
  min-width: 60px;
}

/* 营养概览样式 */
.nutrition-overview {
  margin-bottom: 24px;
}

.nutrition-card {
  height: 100%;
  min-height: 80px;
}

.nutrition-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.nutrition-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.04);
}

.nutrition-content {
  flex: 1;
  min-width: 0;
}

.nutrition-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nutrition-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.nutrition-unit {
  font-size: 12px;
  font-weight: 400;
  color: #999;
  margin-left: 2px;
}

/* 记录列表样式 */
.nutrition-record {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.nutrition-record:last-child {
  border-bottom: none;
}

.empty-records {
  padding: 40px 0;
  text-align: center;
}

/* 移动端适配 */
@media (max-width: 768px) {

  .date-selector {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .date-controls {
    justify-content: stretch;
    align-items: center;
  }

  .date-picker {
    flex: 1;
    min-width: 0;
  }

  .today-button {
    margin-left: 0;
  }

  .nutrition-item {
    gap: 10px;
    padding: 6px 0;
  }

  .nutrition-icon {
    width: 36px;
    height: 36px;
  }

  .nutrition-value {
    font-size: 16px;
  }

  .nutrition-record {
    padding: 16px 0;
  }

  .empty-records {
    padding: 60px 0;
  }
}

@media (max-width: 480px) {
  .date-selector {
    gap: 8px;
  }

  .date-controls {
    gap: 8px;
  }

  .nutrition-item {
    gap: 8px;
    padding: 4px 0;
  }

  .nutrition-icon {
    width: 32px;
    height: 32px;
  }

  .nutrition-label {
    font-size: 11px;
  }

  .nutrition-value {
    font-size: 14px;
  }

  .nutrition-unit {
    font-size: 10px;
  }

  .nutrition-record {
    padding: 12px 0;
  }
}
</style>
