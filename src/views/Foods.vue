<template>
  <div class="foods-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <NText tag="h2" class="page-title">我的食物库</NText>
      <NButton type="primary" @click="addFood" class="add-button">
        <template #icon>
          <NIcon><AddOutline /></NIcon>
        </template>
        <span class="add-button-text">添加食物</span>
      </NButton>
    </div>

    <!-- 搜索和筛选 -->
    <NCard :bordered="false" style="margin-bottom: 16px;">
      <div class="search-filters">
        <div class="search-row">
          <NInput
            v-model:value="searchKeyword"
            placeholder="搜索食物名称或品牌"
            clearable
            class="search-input"
          >
            <template #prefix>
              <NIcon><SearchOutline /></NIcon>
            </template>
          </NInput>
        </div>
        <div class="filter-row">
          <NSelect
            v-model:value="selectedTags"
            multiple
            placeholder="按标签筛选"
            :options="tagOptions"
            clearable
            class="tag-select"
          />
          <div class="switch-container">
            <NSwitch v-model:value="showPublicFoods" class="public-switch">
              <template #checked>显示公共食物</template>
              <template #unchecked>仅显示我的食物</template>
            </NSwitch>
          </div>
        </div>
      </div>
    </NCard>

    <!-- 食物列表 -->
    <NCard :bordered="false" class="food-list-container">
      <!-- 桌面端：数据表格 -->
      <NDataTable
        v-if="!isMobile"
        :columns="columns"
        :data="filteredFoods"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: Food) => row.id"
        class="desktop-table"
      />

      <!-- 移动端：卡片列表 -->
      <MobileFoodList
        v-else
        :foods="paginatedFoods"
        @add-food="addFood"
        @view-food="viewFood"
        @edit-food="editFood"
        @delete-food="handleDelete"
      />

      <!-- 移动端分页 -->
      <div v-if="isMobile && filteredFoods.length > 0" class="mobile-pagination">
        <NPagination
          v-model:page="currentPage"
          :page-count="totalPages"
          :page-size="pageSize"
          show-size-picker
          :page-sizes="[10, 20, 50]"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useResponsive } from '../composables/useResponsive'
import MobileFoodList from '../components/mobile/MobileFoodList.vue'
import {
  NCard,
  NSpace,
  NText,
  NButton,
  NIcon,
  NInput,
  NSelect,
  NSwitch,
  NDataTable,
  NTag,
  NPopconfirm,
  type DataTableColumns
} from 'naive-ui'
import {
  AddOutline,
  SearchOutline,
  CreateOutline,
  TrashOutline
} from '@vicons/ionicons5'
import { useFoodStore, type Food } from '../stores/food'

const router = useRouter()
const message = useMessage()
// const dialog = useDialog() // 暂时未使用
const foodStore = useFoodStore()
const { isMobile } = useResponsive()

const loading = ref(false)
const searchKeyword = ref('')
const selectedTags = ref<number[]>([])
const showPublicFoods = ref(false)

// 移动端分页状态
const currentPage = ref(1)
const pageSize = ref(20)

// 分页配置
const pagination = {
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true
}

// 标签选项
const tagOptions = computed(() => {
  return foodStore.tags.map(tag => ({
    label: tag.name,
    value: tag.id
  }))
})

// 过滤后的食物列表
const filteredFoods = computed(() => {
  let foods = foodStore.foods

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    foods = foods.filter(food => 
      food.name.toLowerCase().includes(keyword) ||
      (food.brand && food.brand.toLowerCase().includes(keyword))
    )
  }

  // 按标签筛选
  if (selectedTags.value.length > 0) {
    foods = foods.filter(food => 
      food.tags?.some(tag => selectedTags.value.includes(tag.id))
    )
  }

  // 是否显示公共食物
  if (!showPublicFoods.value) {
    foods = foods.filter(food => food.user_id === 1) // 假设当前用户ID为1
  }

  return foods
})

// 移动端分页相关计算属性
const totalPages = computed(() => {
  return Math.ceil(filteredFoods.value.length / pageSize.value)
})

const paginatedFoods = computed(() => {
  if (!isMobile.value) return filteredFoods.value

  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredFoods.value.slice(start, end)
})

// 表格列定义
const columns: DataTableColumns<Food> = [
  {
    title: '食物名称',
    key: 'name',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '品牌',
    key: 'brand',
    width: 120,
    render: (row) => row.brand || '-'
  },
  {
    title: '热量',
    key: 'calories',
    width: 100,
    render: (row) => `${row.calories}千卡`
  },
  {
    title: '蛋白质',
    key: 'protein',
    width: 100,
    render: (row) => `${row.protein}g`
  },
  {
    title: '碳水',
    key: 'carbohydrates',
    width: 100,
    render: (row) => `${row.carbohydrates}g`
  },
  {
    title: '脂肪',
    key: 'total_fat',
    width: 100,
    render: (row) => `${row.total_fat}g`
  },
  {
    title: '标签',
    key: 'tags',
    width: 200,
    render: (row) => {
      if (!row.tags || row.tags.length === 0) return '-'
      return h('div', 
        row.tags.map(tag => 
          h(NTag, 
            { 
              size: 'small', 
              color: { color: tag.color, textColor: '#fff' },
              style: { marginRight: '4px' }
            }, 
            { default: () => tag.name }
          )
        )
      )
    }
  },
  {
    title: '类型',
    key: 'is_public',
    width: 80,
    render: (row) => row.is_public ? '公共' : '私有'
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    render: (row) => {
      return h(NSpace, { size: 'small' }, {
        default: () => [
          h(NButton, 
            { 
              size: 'small',
              quaternary: true,
              onClick: () => editFood(row.id)
            },
            { 
              default: () => '编辑',
              icon: () => h(NIcon, null, { default: () => h(CreateOutline) })
            }
          ),
          h(NPopconfirm, 
            {
              onPositiveClick: () => deleteFood(row.id)
            },
            {
              default: () => '确定删除这个食物吗？',
              trigger: () => h(NButton, 
                { 
                  size: 'small',
                  quaternary: true,
                  type: 'error'
                },
                { 
                  default: () => '删除',
                  icon: () => h(NIcon, null, { default: () => h(TrashOutline) })
                }
              )
            }
          )
        ]
      })
    }
  }
]

// 添加食物
const addFood = () => {
  router.push('/foods/add')
}

// 编辑食物
const editFood = (food: Food | number) => {
  const id = typeof food === 'number' ? food : food.id
  router.push(`/foods/${id}/edit`)
}

// 删除食物
const deleteFood = async (id: number) => {
  try {
    loading.value = true
    // 这里应该调用API删除食物
    foodStore.deleteFood(id)
    message.success('食物删除成功')
  } catch (error) {
    message.error('删除失败')
  } finally {
    loading.value = false
  }
}

// 移动端专用方法
const viewFood = (food: Food) => {
  // 可以显示食物详情弹窗或跳转到详情页
  router.push(`/foods/${food.id}`)
}

const handleDelete = async (food: Food) => {
  await deleteFood(food.id)
}

const handlePageSizeChange = (newPageSize: number) => {
  pageSize.value = newPageSize
  currentPage.value = 1 // 重置到第一页
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    // 这里应该调用API加载食物和标签数据
    // await foodStore.loadFoods()
    // await foodStore.loadTags()
  } catch (error) {
    message.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.foods-page {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;
}

.page-title {
  margin: 0;
  flex: 1;
}

.add-button {
  flex-shrink: 0;
}

.add-button-text {
  margin-left: 4px;
}

/* 搜索筛选样式 */
.search-filters {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.search-row {
  display: flex;
}

.search-input {
  width: 100%;
  max-width: 400px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.tag-select {
  flex: 1;
  min-width: 200px;
  max-width: 300px;
}

.switch-container {
  flex-shrink: 0;
}

.public-switch {
  white-space: nowrap;
}

/* 食物列表容器 */
.food-list-container {
  width: 100%;
}

.desktop-table {
  width: 100%;
}

.mobile-pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 移动端适配 */
@media (max-width: 768px) {

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .page-title {
    text-align: center;
    font-size: 18px;
  }

  .add-button {
    width: 100%;
  }

  .search-input {
    max-width: 100%;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .tag-select {
    max-width: 100%;
  }

  .switch-container {
    display: flex;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .page-header {
    gap: 8px;
  }

  .page-title {
    font-size: 16px;
  }

  .add-button-text {
    display: none;
  }

  .search-filters {
    gap: 8px;
  }

  .filter-row {
    gap: 8px;
  }
}
</style>
