<template>
  <div class="tags-page">
    <NSpace justify="space-between" align="center" style="margin-bottom: 16px;">
      <NText tag="h2" style="margin: 0;">标签管理</NText>
      <NButton type="primary" @click="showAddModal = true">
        <template #icon>
          <NIcon><AddOutline /></NIcon>
        </template>
        添加标签
      </NButton>
    </NSpace>

    <!-- 系统标签 -->
    <NCard title="系统标签" :bordered="false" style="margin-bottom: 16px;">
      <NSpace>
        <NTag
          v-for="tag in systemTags"
          :key="tag.id"
          :color="{ color: tag.color, textColor: '#fff' }"
          size="large"
        >
          {{ tag.name }}
        </NTag>
      </NSpace>
      <NEmpty v-if="systemTags.length === 0" description="暂无系统标签" />
    </NCard>

    <!-- 自定义标签 -->
    <NCard title="我的标签" :bordered="false">
      <NSpace>
        <NTag
          v-for="tag in userTags"
          :key="tag.id"
          :color="{ color: tag.color, textColor: '#fff' }"
          size="large"
          closable
          @close="deleteTag(tag.id)"
          @click="editTag(tag)"
        >
          {{ tag.name }}
        </NTag>
      </NSpace>
      <NEmpty v-if="userTags.length === 0" description="暂无自定义标签" />
    </NCard>

    <!-- 添加/编辑标签模态框 -->
    <NModal v-model:show="showAddModal" preset="dialog" title="添加标签">
      <NForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="80"
      >
        <NFormItem label="标签名称" path="name">
          <NInput
            v-model:value="formData.name"
            placeholder="请输入标签名称"
            maxlength="20"
            show-count
          />
        </NFormItem>
        <NFormItem label="标签颜色" path="color">
          <NColorPicker v-model:value="formData.color" />
        </NFormItem>
        <NFormItem label="描述" path="description">
          <NInput
            v-model:value="formData.description"
            type="textarea"
            placeholder="标签描述（可选）"
            :rows="3"
          />
        </NFormItem>
      </NForm>
      <template #action>
        <NSpace>
          <NButton @click="showAddModal = false">取消</NButton>
          <NButton type="primary" @click="handleSaveTag" :loading="loading">
            {{ editingTag ? '更新' : '添加' }}
          </NButton>
        </NSpace>
      </template>
    </NModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import {
  NCard,
  NSpace,
  NText,
  NButton,
  NIcon,
  NTag,
  NEmpty,
  NModal,
  NForm,
  NFormItem,
  NInput,
  NColorPicker,
  type FormInst,
  type FormRules
} from 'naive-ui'
import { AddOutline } from '@vicons/ionicons5'
import { useFoodStore, type FoodTag } from '../stores/food'

const message = useMessage()
const foodStore = useFoodStore()

const formRef = ref<FormInst | null>(null)
const loading = ref(false)
const showAddModal = ref(false)
const editingTag = ref<FoodTag | null>(null)

const formData = reactive({
  name: '',
  color: '#1890ff',
  description: ''
})

const rules: FormRules = {
  name: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { min: 1, max: 20, message: '标签名称长度为1-20个字符', trigger: 'blur' }
  ]
}

const systemTags = computed(() => {
  return foodStore.tags.filter(tag => tag.is_system)
})

const userTags = computed(() => {
  return foodStore.tags.filter(tag => !tag.is_system)
})

const resetForm = () => {
  formData.name = ''
  formData.color = '#1890ff'
  formData.description = ''
  editingTag.value = null
}

const editTag = (tag: FoodTag) => {
  editingTag.value = tag
  formData.name = tag.name
  formData.color = tag.color
  formData.description = tag.description || ''
  showAddModal.value = true
}

const handleSaveTag = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    const tagData = {
      id: editingTag.value?.id || Date.now(),
      name: formData.name,
      color: formData.color,
      description: formData.description,
      is_system: false,
      user_id: 1 // 假设当前用户ID为1
    }

    if (editingTag.value) {
      // 更新标签
      foodStore.updateTag(editingTag.value.id, tagData)
      message.success('标签更新成功')
    } else {
      // 添加标签
      foodStore.addTag(tagData)
      message.success('标签添加成功')
    }

    showAddModal.value = false
    resetForm()
  } catch (error) {
    message.error('请检查表单填写是否正确')
  } finally {
    loading.value = false
  }
}

const deleteTag = async (id: number) => {
  try {
    foodStore.deleteTag(id)
    message.success('标签删除成功')
  } catch (error) {
    message.error('删除失败')
  }
}

onMounted(() => {
  // 加载标签数据
  // foodStore.loadTags()
})
</script>

<style scoped>
.tags-page {
  max-width: 800px;
  margin: 0 auto;
}

.n-tag {
  cursor: pointer;
  margin: 4px;
}
</style>
