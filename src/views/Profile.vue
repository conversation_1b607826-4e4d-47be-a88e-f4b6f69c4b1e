<template>
  <div class="profile-page">
    <NCard title="个人资料" :bordered="false">
      <NForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-placement="isMobile ? 'top' : 'left'"
        :label-width="isMobile ? 'auto' : '120'"
        class="profile-form"
      >
        <NGrid :cols="isMobile ? 1 : 2" :x-gap="24" responsive="screen">
          <NGridItem>
            <NFormItem label="用户名" path="username">
              <NInput
                v-model:value="formData.username"
                placeholder="用户名"
                disabled
              />
            </NFormItem>
          </NGridItem>
          <NGridItem>
            <NFormItem label="邮箱" path="email">
              <NInput
                v-model:value="formData.email"
                placeholder="邮箱地址"
                disabled
              />
            </NFormItem>
          </NGridItem>
        </NGrid>

        <NGrid :cols="isMobile ? 1 : 2" :x-gap="24" responsive="screen">
          <NGridItem>
            <NFormItem label="昵称" path="nickname">
              <NInput
                v-model:value="formData.nickname"
                placeholder="请输入昵称"
                clearable
              />
            </NFormItem>
          </NGridItem>
          <NGridItem>
            <NFormItem label="性别" path="gender">
              <NSelect
                v-model:value="formData.gender"
                :options="genderOptions"
                placeholder="请选择性别"
                clearable
              />
            </NFormItem>
          </NGridItem>
        </NGrid>

        <NFormItem label="出生日期" path="birth_date">
          <NDatePicker
            v-model:value="formData.birth_date"
            type="date"
            placeholder="请选择出生日期"
            style="width: 300px;"
          />
        </NFormItem>

        <NGrid :cols="2" :x-gap="24">
          <NGridItem>
            <NFormItem label="身高" path="height">
              <NInputNumber
                v-model:value="formData.height"
                :min="100"
                :max="250"
                :precision="1"
                placeholder="身高"
                style="width: 100%"
              >
                <template #suffix>cm</template>
              </NInputNumber>
            </NFormItem>
          </NGridItem>
          <NGridItem>
            <NFormItem label="体重" path="weight">
              <NInputNumber
                v-model:value="formData.weight"
                :min="30"
                :max="200"
                :precision="1"
                placeholder="体重"
                style="width: 100%"
              >
                <template #suffix>kg</template>
              </NInputNumber>
            </NFormItem>
          </NGridItem>
        </NGrid>

        <NFormItem label="活动水平" path="activity_level">
          <NSelect
            v-model:value="formData.activity_level"
            :options="activityLevelOptions"
            placeholder="请选择活动水平"
          />
        </NFormItem>

        <!-- BMI 计算结果 -->
        <div v-if="bmi" class="bmi-info">
          <NDivider title-placement="left">健康指标</NDivider>
          <NSpace>
            <NStatistic label="BMI" :value="bmi" :precision="1">
              <template #suffix>
                <NTag :type="bmiStatus.type" size="small" style="margin-left: 8px;">
                  {{ bmiStatus.text }}
                </NTag>
              </template>
            </NStatistic>
            <NStatistic 
              label="基础代谢率" 
              :value="bmr" 
              suffix="千卡/天"
              v-if="bmr"
            />
          </NSpace>
        </div>

        <NSpace justify="end" style="margin-top: 24px;">
          <NButton @click="handleReset">重置</NButton>
          <NButton type="primary" @click="handleSave" :loading="loading">
            保存
          </NButton>
        </NSpace>
      </NForm>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import dayjs from 'dayjs'
import {
  NCard,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NSelect,
  NDatePicker,
  NButton,
  NSpace,
  NGrid,
  NGridItem,
  NDivider,
  NStatistic,
  NTag,
  type FormInst,
  type FormRules
} from 'naive-ui'
import { useUserStore, type User } from '../stores/user'

const message = useMessage()
const userStore = useUserStore()

const formRef = ref<FormInst | null>(null)
const loading = ref(false)
const isMobile = ref(false)

const formData = reactive({
  username: '',
  email: '',
  nickname: '',
  gender: null as string | null,
  birth_date: null as number | null,
  height: null as number | null,
  weight: null as number | null,
  activity_level: null as string | null
})

const rules: FormRules = {
  nickname: [
    { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
  ],
  height: [
    { type: 'number', min: 100, max: 250, message: '身高应在100-250cm之间', trigger: 'blur' }
  ],
  weight: [
    { type: 'number', min: 30, max: 200, message: '体重应在30-200kg之间', trigger: 'blur' }
  ]
}

const genderOptions = [
  { label: '男', value: 'male' },
  { label: '女', value: 'female' },
  { label: '其他', value: 'other' }
]

const activityLevelOptions = [
  { label: '久坐不动', value: 'sedentary' },
  { label: '轻度活动', value: 'light' },
  { label: '中度活动', value: 'moderate' },
  { label: '高度活动', value: 'active' },
  { label: '极高活动', value: 'very_active' }
]

// BMI 计算
const bmi = computed(() => {
  if (!formData.height || !formData.weight) return null
  const heightInM = formData.height / 100
  return formData.weight / (heightInM * heightInM)
})

// BMI 状态
const bmiStatus = computed(() => {
  if (!bmi.value) return { type: 'default' as const, text: '' }

  if (bmi.value < 18.5) return { type: 'warning' as const, text: '偏瘦' }
  if (bmi.value < 24) return { type: 'success' as const, text: '正常' }
  if (bmi.value < 28) return { type: 'warning' as const, text: '超重' }
  return { type: 'error' as const, text: '肥胖' }
})

// 基础代谢率计算 (Harris-Benedict公式)
const bmr = computed(() => {
  if (!formData.height || !formData.weight || !formData.birth_date || !formData.gender) {
    return null
  }
  
  const age = dayjs().diff(dayjs(formData.birth_date), 'year')
  
  if (formData.gender === 'male') {
    return Math.round(88.362 + (13.397 * formData.weight) + (4.799 * formData.height) - (5.677 * age))
  } else if (formData.gender === 'female') {
    return Math.round(447.593 + (9.247 * formData.weight) + (3.098 * formData.height) - (4.330 * age))
  }
  
  return null
})

const handleReset = () => {
  if (userStore.user) {
    loadUserData(userStore.user)
  }
}

const handleSave = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    const updates: Partial<User> = {
      nickname: formData.nickname || undefined,
      gender: formData.gender as any,
      birth_date: formData.birth_date ? dayjs(formData.birth_date).format('YYYY-MM-DD') : undefined,
      height: formData.height || undefined,
      weight: formData.weight || undefined,
      activity_level: formData.activity_level as any
    }

    // 这里应该调用API更新用户信息
    userStore.updateProfile(updates)
    message.success('个人资料更新成功')
  } catch (error) {
    message.error('请检查表单填写是否正确')
  } finally {
    loading.value = false
  }
}

const loadUserData = (user: User) => {
  formData.username = user.username
  formData.email = user.email
  formData.nickname = user.nickname || ''
  formData.gender = user.gender || null
  formData.birth_date = user.birth_date ? dayjs(user.birth_date).valueOf() : null
  formData.height = user.height || null
  formData.weight = user.weight || null
  formData.activity_level = user.activity_level || null
}

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

onMounted(() => {
  if (userStore.user) {
    loadUserData(userStore.user)
  }

  // 检测移动端
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.profile-page {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  padding: 0;
}

.profile-form {
  width: 100%;
}

.bmi-info {
  margin: 16px 0;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

/* 移动端适配 */
@media (max-width: 768px) {

  .bmi-info {
    margin: 12px 0;
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .bmi-info {
    margin: 8px 0;
    padding: 8px;
  }
}
</style>
