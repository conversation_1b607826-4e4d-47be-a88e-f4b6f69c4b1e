<template>
  <div class="add-record-container">
    <NCard title="添加营养记录" :bordered="false">
      <NForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="top"
      >
        <NGrid :cols="2" :x-gap="16" responsive="screen">
          <NFormItemGi label="日期" path="record_date">
            <NDatePicker
              v-model:value="formData.record_date"
              type="date"
              style="width: 100%"
            />
          </NFormItemGi>
          <NFormItemGi label="时间" path="record_time">
            <NTimePicker
              v-model:value="formData.record_time"
              format="HH:mm"
              style="width: 100%"
            />
          </NFormItemGi>
        </NGrid>

        <NFormItem label="食物类型" path="food_type">
          <NRadioGroup v-model:value="formData.food_type" @update:value="handleFoodTypeChange">
            <NSpace>
              <NRadio value="existing">选择已有食物</NRadio>
              <NRadio value="custom">自定义食物</NRadio>
            </NSpace>
          </NRadioGroup>
        </NFormItem>

        <NFormItem
          v-if="formData.food_type === 'existing'"
          label="选择食物"
          path="food_id"
        >
          <NSelect
            v-model:value="formData.food_id"
            :options="foodOptions"
            placeholder="搜索并选择食物"
            filterable
            clearable
            @update:value="handleFoodChange"
          />
        </NFormItem>

        <NFormItem
          v-if="formData.food_type === 'custom'"
          label="食物名称"
          path="food_name"
        >
          <NInput
            v-model:value="formData.food_name"
            placeholder="输入食物名称"
            clearable
          />
        </NFormItem>

        <NGrid :cols="2" :x-gap="16" responsive="screen">
          <NFormItemGi label="摄入量" path="quantity">
            <NInputNumber
              v-model:value="formData.quantity"
              :min="0"
              :precision="2"
              placeholder="0"
              style="width: 100%"
            />
          </NFormItemGi>
          <NFormItemGi label="单位" path="unit">
            <NSelect
              v-model:value="formData.unit"
              :options="unitOptions"
              placeholder="选择单位"
            />
          </NFormItemGi>
        </NGrid>

        <!-- 自定义营养成分 -->
        <div v-if="formData.food_type === 'custom'">
          <NDivider title-placement="left">营养成分 (每100g)</NDivider>
          <NGrid :cols="2" :x-gap="16" :y-gap="16" responsive="screen">
            <NFormItemGi label="热量 (千卡)" path="custom_calories">
              <NInputNumber
                v-model:value="formData.custom_nutrition.calories"
                :min="0"
                :precision="2"
                placeholder="0"
                style="width: 100%"
              />
            </NFormItemGi>
            <NFormItemGi label="蛋白质 (g)" path="custom_protein">
              <NInputNumber
                v-model:value="formData.custom_nutrition.protein"
                :min="0"
                :precision="2"
                placeholder="0"
                style="width: 100%"
              />
            </NFormItemGi>
            <NFormItemGi label="碳水化合物 (g)" path="custom_carbohydrates">
              <NInputNumber
                v-model:value="formData.custom_nutrition.carbohydrates"
                :min="0"
                :precision="2"
                placeholder="0"
                style="width: 100%"
              />
            </NFormItemGi>
            <NFormItemGi label="脂肪 (g)" path="custom_fat">
              <NInputNumber
                v-model:value="formData.custom_nutrition.total_fat"
                :min="0"
                :precision="2"
                placeholder="0"
                style="width: 100%"
              />
            </NFormItemGi>
          </NGrid>
        </div>

        <!-- 营养成分预览 -->
        <div v-if="selectedFood && formData.quantity > 0" class="nutrition-preview">
          <NDivider title-placement="left">营养成分预览</NDivider>
          <NGrid :cols="4" :x-gap="16" :y-gap="8">
            <NGridItem>
              <NStatistic 
                label="热量" 
                :value="calculatedNutrition.calories" 
                suffix="千卡"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic 
                label="蛋白质" 
                :value="calculatedNutrition.protein" 
                suffix="g"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic 
                label="碳水化合物" 
                :value="calculatedNutrition.carbohydrates" 
                suffix="g"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic 
                label="脂肪" 
                :value="calculatedNutrition.fat" 
                suffix="g"
              />
            </NGridItem>
          </NGrid>
        </div>

        <NFormItem label="备注" path="notes">
          <NInput
            v-model:value="formData.notes"
            type="textarea"
            placeholder="备注信息（可选）"
            :rows="3"
          />
        </NFormItem>

        <NSpace justify="space-between" style="margin-top: 24px;">
          <NButton @click="handleCancel">取消</NButton>
          <NButton type="primary" @click="handleSave" :loading="loading">保存</NButton>
        </NSpace>
      </NForm>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
import dayjs from 'dayjs'
import {
  NCard,
  NForm,
  NFormItem,
  NFormItemGi,
  NDatePicker,
  NTimePicker,
  NSelect,
  NInputNumber,
  NInput,
  NButton,
  NSpace,
  NGrid,
  NGridItem,
  NDivider,
  NStatistic,
  NRadioGroup,
  NRadio,
  type FormInst,
  type FormRules
} from 'naive-ui'
import { useFoodStore } from '../stores/food'
import { useNutritionStore } from '../stores/nutrition'

const router = useRouter()
const route = useRoute()
const message = useMessage()
const foodStore = useFoodStore()
const nutritionStore = useNutritionStore()

const formRef = ref<FormInst | null>(null)
const loading = ref(false)

const formData = reactive({
  record_date: Date.now(),
  record_time: Date.now(),
  food_type: 'existing' as 'existing' | 'custom',
  food_id: null as number | null,
  food_name: '',
  quantity: 0,
  unit: 'g',
  notes: '',
  custom_nutrition: {
    calories: 0,
    protein: 0,
    carbohydrates: 0,
    total_fat: 0,
    saturated_fat: 0,
    trans_fat: 0,
    cholesterol: 0,
    sodium: 0,
    potassium: 0,
    dietary_fiber: 0,
    sugar: 0,
    vitamin_a: 0,
    vitamin_c: 0,
    calcium: 0,
    iron: 0
  }
})

const rules: FormRules = {
  record_date: [
    { required: true, message: '请选择日期', trigger: 'blur' }
  ],
  record_time: [
    { required: true, message: '请选择时间', trigger: 'blur' }
  ],
  food_id: [
    {
      required: true,
      message: '请选择食物',
      trigger: 'change',
      validator: () => {
        if (formData.food_type === 'existing' && !formData.food_id) {
          return new Error('请选择食物')
        }
        return true
      }
    }
  ],
  food_name: [
    {
      required: true,
      message: '请输入食物名称',
      trigger: 'blur',
      validator: () => {
        if (formData.food_type === 'custom' && !formData.food_name.trim()) {
          return new Error('请输入食物名称')
        }
        return true
      }
    }
  ],
  quantity: [
    { required: true, message: '请输入摄入量', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '摄入量必须大于0', trigger: 'blur' }
  ]
}

// 移除餐次选项，不再需要

const unitOptions = [
  { label: '克 (g)', value: 'g' },
  { label: '毫升 (ml)', value: 'ml' },
  { label: '份', value: '份' },
  { label: '个', value: '个' },
  { label: '片', value: '片' },
  { label: '杯', value: '杯' }
]

const foodOptions = computed(() => {
  return foodStore.foods.map(food => ({
    label: `${food.name}${food.brand ? ` (${food.brand})` : ''}`,
    value: food.id
  }))
})

const selectedFood = computed(() => {
  if (!formData.food_id) return null
  return foodStore.foods.find(f => f.id === formData.food_id) || null
})

const calculatedNutrition = computed(() => {
  if (formData.food_type === 'custom') {
    if (!formData.quantity) {
      return { calories: 0, protein: 0, carbohydrates: 0, fat: 0 }
    }
    const ratio = formData.quantity / 100
    return {
      calories: Math.round(formData.custom_nutrition.calories * ratio * 100) / 100,
      protein: Math.round(formData.custom_nutrition.protein * ratio * 100) / 100,
      carbohydrates: Math.round(formData.custom_nutrition.carbohydrates * ratio * 100) / 100,
      fat: Math.round(formData.custom_nutrition.total_fat * ratio * 100) / 100
    }
  }

  if (!selectedFood.value || !formData.quantity) {
    return { calories: 0, protein: 0, carbohydrates: 0, fat: 0 }
  }

  const food = selectedFood.value
  const ratio = formData.quantity / 100 // 营养成分是按100g计算的

  return {
    calories: Math.round(food.calories * ratio * 100) / 100,
    protein: Math.round(food.protein * ratio * 100) / 100,
    carbohydrates: Math.round(food.carbohydrates * ratio * 100) / 100,
    fat: Math.round(food.total_fat * ratio * 100) / 100
  }
})

const handleFoodTypeChange = () => {
  // 切换食物类型时重置相关字段
  formData.food_id = null
  formData.food_name = ''
  // 重置自定义营养成分
  Object.keys(formData.custom_nutrition).forEach(key => {
    formData.custom_nutrition[key as keyof typeof formData.custom_nutrition] = 0
  })
}

const handleFoodChange = () => {
  // 食物变化时可以做一些处理
}

const handleCancel = () => {
  router.back()
}

const handleSave = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    // 计算实际营养成分
    const nutrition = calculatedNutrition.value
    const ratio = formData.quantity / 100

    let recordData: any = {
      id: Date.now(), // 临时ID
      user_id: 1, // 假设当前用户ID为1
      record_date: dayjs(formData.record_date).format('YYYY-MM-DD'),
      record_time: dayjs(formData.record_time).format('HH:mm'),
      quantity: formData.quantity,
      unit: formData.unit,
      actual_calories: nutrition.calories,
      actual_carbohydrates: nutrition.carbohydrates,
      actual_total_fat: nutrition.fat,
      actual_protein: nutrition.protein,
      notes: formData.notes
    }

    if (formData.food_type === 'existing') {
      const food = selectedFood.value
      if (!food) {
        message.error('请选择食物')
        return
      }

      recordData = {
        ...recordData,
        food_id: formData.food_id!,
        food: food,
        actual_saturated_fat: food.saturated_fat * ratio,
        actual_trans_fat: food.trans_fat * ratio,
        actual_cholesterol: food.cholesterol * ratio,
        actual_sodium: food.sodium * ratio,
        actual_potassium: food.potassium * ratio,
        actual_dietary_fiber: food.dietary_fiber * ratio,
        actual_sugar: food.sugar * ratio,
        actual_vitamin_a: food.vitamin_a * ratio,
        actual_vitamin_c: food.vitamin_c * ratio,
        actual_calcium: food.calcium * ratio,
        actual_iron: food.iron * ratio
      }
    } else {
      // 自定义食物
      recordData = {
        ...recordData,
        food_name: formData.food_name,
        actual_saturated_fat: formData.custom_nutrition.saturated_fat * ratio,
        actual_trans_fat: formData.custom_nutrition.trans_fat * ratio,
        actual_cholesterol: formData.custom_nutrition.cholesterol * ratio,
        actual_sodium: formData.custom_nutrition.sodium * ratio,
        actual_potassium: formData.custom_nutrition.potassium * ratio,
        actual_dietary_fiber: formData.custom_nutrition.dietary_fiber * ratio,
        actual_sugar: formData.custom_nutrition.sugar * ratio,
        actual_vitamin_a: formData.custom_nutrition.vitamin_a * ratio,
        actual_vitamin_c: formData.custom_nutrition.vitamin_c * ratio,
        actual_calcium: formData.custom_nutrition.calcium * ratio,
        actual_iron: formData.custom_nutrition.iron * ratio
      }
    }

    // 这里应该调用API保存记录
    nutritionStore.addRecord(recordData)
    
    message.success('营养记录添加成功')
    router.push('/dashboard')
  } catch (error) {
    message.error('请检查表单填写是否正确')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  try {
    // 从查询参数中获取预设值
    if (route.query.date) {
      const dateValue = dayjs(route.query.date as string)
      if (dateValue.isValid()) {
        formData.record_date = dateValue.valueOf()
      } else {
        formData.record_date = Date.now()
      }
    } else {
      formData.record_date = Date.now()
    }

    if (route.query.time) {
      const timeValue = dayjs(route.query.time as string, 'HH:mm')
      if (timeValue.isValid()) {
        formData.record_time = timeValue.valueOf()
      } else {
        formData.record_time = dayjs().valueOf()
      }
    } else {
      formData.record_time = dayjs().valueOf()
    }
  } catch (error) {
    console.error('初始化日期时间错误:', error)
    formData.record_date = Date.now()
    formData.record_time = dayjs().valueOf()
  }
})
</script>

<style scoped>
.add-record-container {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  padding: 0;
}

.nutrition-preview {
  margin: 16px 0;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

/* 移动端适配 */
@media (max-width: 768px) {

  .nutrition-preview {
    margin: 12px 0;
    padding: 12px;
  }
}
</style>
