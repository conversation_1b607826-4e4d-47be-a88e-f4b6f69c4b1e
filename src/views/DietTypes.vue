<template>
  <div class="diet-types-page">
    <div class="page-header">
      <h1>饮食类型管理</h1>
      <NButton type="primary" @click="showAddModal = true">
        <template #icon>
          <NIcon><AddOutline /></NIcon>
        </template>
        添加饮食类型
      </NButton>
    </div>

    <div class="diet-types-grid">
      <NCard
        v-for="dietType in dietTypes"
        :key="dietType.id"
        class="diet-type-card"
        hoverable
      >
        <template #header>
          <div class="card-header">
            <div class="diet-type-info">
              <div 
                class="color-indicator" 
                :style="{ backgroundColor: dietType.color }"
              ></div>
              <div>
                <h3>{{ dietType.name }}</h3>
                <p class="english-name">{{ dietType.nameEn }}</p>
              </div>
            </div>
            <div class="card-actions">
              <NButton
                quaternary
                circle
                size="small"
                @click="viewDietType(dietType)"
              >
                <template #icon>
                  <NIcon><EyeOutline /></NIcon>
                </template>
              </NButton>
              <NButton
                v-if="!dietType.isSystem"
                quaternary
                circle
                size="small"
                @click="editDietType(dietType)"
              >
                <template #icon>
                  <NIcon><CreateOutline /></NIcon>
                </template>
              </NButton>
              <NButton
                v-if="!dietType.isSystem"
                quaternary
                circle
                size="small"
                type="error"
                @click="deleteDietType(dietType)"
              >
                <template #icon>
                  <NIcon><TrashOutline /></NIcon>
                </template>
              </NButton>
            </div>
          </div>
        </template>

        <div class="card-content">
          <p class="description">{{ dietType.description }}</p>
          
          <div class="features-section">
            <h4>营养特点</h4>
            <p>{{ dietType.nutritionFeatures }}</p>
          </div>

          <div class="benefits-section">
            <h4>主要益处</h4>
            <p>{{ dietType.benefits }}</p>
          </div>

          <div class="suitable-people-section">
            <h4>适合人群</h4>
            <p>{{ dietType.suitablePeople }}</p>
          </div>

          <div class="system-badge" v-if="dietType.isSystem">
            <NTag type="info" size="small">系统预设</NTag>
          </div>
        </div>

        <template #action>
          <NButton
            block
            @click="viewFoodCategories(dietType)"
          >
            查看食物分类 ({{ getFoodCategoriesCount(dietType.id) }})
          </NButton>
        </template>
      </NCard>
    </div>

    <!-- 添加/编辑饮食类型模态框 -->
    <NModal
      v-model:show="showAddModal"
      preset="card"
      style="width: 600px;"
      :title="editingDietType ? '编辑饮食类型' : '添加饮食类型'"
    >
      <DietTypeForm
        :diet-type="editingDietType"
        @save="handleSaveDietType"
        @cancel="handleCancelEdit"
      />
    </NModal>

    <!-- 查看饮食类型详情模态框 -->
    <NModal v-model:show="showViewModal" preset="card" style="width: 600px;" title="饮食类型详情">
      <DietTypeDetail
        v-if="viewingDietType"
        :diet-type="viewingDietType"
        :food-categories="getFoodCategoriesByDietType(viewingDietType.id)"
      />
    </NModal>

    <!-- 食物分类模态框 -->
    <NModal v-model:show="showCategoriesModal" preset="card" style="width: 800px;" title="食物分类">
      <FoodCategoriesList
        v-if="selectedDietType"
        :diet-type="selectedDietType"
        :categories="getFoodCategoriesByDietType(selectedDietType.id)"
      />
    </NModal>

    <!-- 加载状态 -->
    <NSpin :show="loading" style="width: 100%;">
      <div style="height: 200px;"></div>
    </NSpin>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import {
  NCard,
  NButton,
  NIcon,
  NModal,
  NTag,
  NSpin,
  useDialog,
  useMessage
} from 'naive-ui'
import {
  AddOutline,
  EyeOutline,
  CreateOutline,
  TrashOutline
} from '@vicons/ionicons5'
import { useDietTypeStore, type DietType } from '../stores/dietType'
import DietTypeForm from '../components/DietTypeForm.vue'
import DietTypeDetail from '../components/DietTypeDetail.vue'
import FoodCategoriesList from '../components/FoodCategoriesList.vue'

const dietTypeStore = useDietTypeStore()
const dialog = useDialog()
const message = useMessage()

// 响应式状态
const showAddModal = ref(false)
const showViewModal = ref(false)
const showCategoriesModal = ref(false)
const editingDietType = ref<DietType | null>(null)
const viewingDietType = ref<DietType | null>(null)
const selectedDietType = ref<DietType | null>(null)

// 计算属性
const dietTypes = computed(() => dietTypeStore.dietTypes)
const loading = computed(() => dietTypeStore.loading)
const getFoodCategoriesByDietType = computed(() => dietTypeStore.getFoodCategoriesByDietType)

const getFoodCategoriesCount = (dietTypeId: number) => {
  return getFoodCategoriesByDietType.value(dietTypeId).length
}

// 方法
const viewDietType = (dietType: DietType) => {
  viewingDietType.value = dietType
  showViewModal.value = true
}

const editDietType = (dietType: DietType) => {
  editingDietType.value = { ...dietType }
  showAddModal.value = true
}

const deleteDietType = (dietType: DietType) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除饮食类型"${dietType.name}"吗？此操作不可撤销。`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await dietTypeStore.deleteDietType(dietType.id)
        message.success('删除成功')
      } catch (error) {
        message.error(error instanceof Error ? error.message : '删除失败')
      }
    }
  })
}

const viewFoodCategories = (dietType: DietType) => {
  selectedDietType.value = dietType
  showCategoriesModal.value = true
}

const handleSaveDietType = async (dietTypeData: Omit<DietType, 'id' | 'createdAt' | 'updatedAt'>) => {
  try {
    if (editingDietType.value?.id) {
      // 编辑模式
      await dietTypeStore.updateDietType(editingDietType.value.id, dietTypeData)
      message.success('更新成功')
    } else {
      // 添加模式
      await dietTypeStore.createDietType(dietTypeData)
      message.success('添加成功')
    }
    showAddModal.value = false
    editingDietType.value = null
  } catch (error) {
    message.error(error instanceof Error ? error.message : '保存失败')
  }
}

const handleCancelEdit = () => {
  showAddModal.value = false
  editingDietType.value = null
}

// 生命周期
onMounted(async () => {
  await dietTypeStore.fetchDietTypes()
  await dietTypeStore.fetchFoodCategories()
})
</script>

<style scoped>
.diet-types-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.diet-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.diet-type-card {
  height: fit-content;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.diet-type-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.color-indicator {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

.diet-type-info h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.english-name {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #666;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-content {
  position: relative;
  padding-top: 8px; /* 为系统标签留出空间 */
}

.description {
  margin-bottom: 16px;
  line-height: 1.6;
  color: #333;
}

.features-section,
.benefits-section,
.suitable-people-section {
  margin-bottom: 16px;
}

.features-section h4,
.benefits-section h4,
.suitable-people-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #666;
}

.features-section p,
.benefits-section p,
.suitable-people-section p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

.system-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .diet-types-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .card-actions {
    align-self: flex-end;
  }
}
</style>
