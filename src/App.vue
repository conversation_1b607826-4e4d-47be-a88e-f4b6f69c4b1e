<script setup lang="ts">
import { zhCN, dateZhCN } from 'naive-ui'
import AppLayout from './components/AppLayout.vue'
</script>

<template>
  <NConfigProvider :locale="zhCN" :date-locale="dateZhCN">
    <NMessageProvider>
      <NDialogProvider>
        <AppLayout />
      </NDialogProvider>
    </NMessageProvider>
  </NConfigProvider>
</template>

<style>
/* 确保全屏布局 */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
}

#app {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* 重置 Naive UI 的一些默认样式 */
.n-config-provider {
  width: 100%;
  height: 100%;
}

/* 确保所有 Provider 组件都是全宽 */
.n-message-provider,
.n-dialog-provider {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
