// 测试饮食类型和每日饮食计划功能
import { useDietTypeStore } from '../stores/dietType'
import { useDailyDietPlanStore } from '../stores/dailyDietPlan'

export const testDietFeatures = async () => {
  console.log('🧪 开始测试饮食功能...')
  
  try {
    // 测试饮食类型功能
    console.log('📋 测试饮食类型功能...')
    const dietTypeStore = useDietTypeStore()
    
    // 加载饮食类型数据
    await dietTypeStore.fetchDietTypes()
    await dietTypeStore.fetchFoodCategories()
    
    console.log(`✅ 成功加载 ${dietTypeStore.dietTypes.length} 种饮食类型`)
    console.log(`✅ 成功加载 ${dietTypeStore.foodCategories.length} 个食物分类`)
    
    // 测试获取地中海饮食的食物分类
    const mediterraneanCategories = dietTypeStore.getFoodCategoriesByDietType(1)
    console.log(`✅ 地中海饮食包含 ${mediterraneanCategories.length} 个食物分类`)
    
    // 测试每日饮食计划功能
    console.log('📅 测试每日饮食计划功能...')
    const dailyPlanStore = useDailyDietPlanStore()
    
    // 加载每日计划数据
    await dailyPlanStore.fetchDailyPlans(1)
    console.log(`✅ 成功加载 ${dailyPlanStore.dailyPlans.length} 个饮食计划`)
    
    // 测试今日计划
    const todayPlan = dailyPlanStore.getTodayPlan
    if (todayPlan) {
      await dailyPlanStore.fetchPlanItems(todayPlan.id)
      const todayItems = dailyPlanStore.getTodayPlanItems
      console.log(`✅ 今日计划包含 ${todayItems.length} 个食物分类`)
      
      // 测试完成度计算
      const completionRate = dailyPlanStore.getCompletionRate(todayPlan.id)
      console.log(`✅ 今日完成度: ${completionRate}%`)
    }
    
    console.log('🎉 所有功能测试通过！')
    return true
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
    return false
  }
}

// 在开发环境下自动运行测试
if (import.meta.env.DEV) {
  // 延迟执行，确保应用已初始化
  setTimeout(() => {
    testDietFeatures()
  }, 2000)
}
