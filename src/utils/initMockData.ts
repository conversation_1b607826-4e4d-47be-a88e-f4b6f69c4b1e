import { useFoodStore } from '../stores/food'
import { useNutritionStore } from '../stores/nutrition'
import { useUserStore } from '../stores/user'
import { 
  mockFoods, 
  mockFoodTags, 
  mockNutritionRecords, 
  mockUser 
} from '../data/mockData'

/**
 * 初始化测试数据
 * 在开发环境中使用，用于展示应用功能
 */
export function initMockData() {
  const foodStore = useFoodStore()
  const nutritionStore = useNutritionStore()
  const userStore = useUserStore()

  try {
    // 初始化食物标签
    foodStore.setTags(mockFoodTags)
    console.log('✅ 食物标签数据已初始化')

    // 初始化食物数据
    foodStore.setFoods(mockFoods)
    console.log('✅ 食物数据已初始化')

    // 初始化营养记录
    nutritionStore.setRecords(mockNutritionRecords)
    console.log('✅ 营养记录数据已初始化')

    // 初始化用户数据
    userStore.setUser(mockUser)
    console.log('✅ 用户数据已初始化')

    // 计算并设置每日汇总
    const today = '2024-01-15'
    const todayRecords = mockNutritionRecords.filter(record => record.record_date === today)
    
    if (todayRecords.length > 0) {
      const dailySummary = {
        id: 1,
        user_id: 1,
        summary_date: today,
        total_calories: todayRecords.reduce((sum, record) => sum + record.actual_calories, 0),
        total_carbohydrates: todayRecords.reduce((sum, record) => sum + record.actual_carbohydrates, 0),
        total_fat: todayRecords.reduce((sum, record) => sum + record.actual_total_fat, 0),
        total_protein: todayRecords.reduce((sum, record) => sum + record.actual_protein, 0),
        total_saturated_fat: todayRecords.reduce((sum, record) => sum + record.actual_saturated_fat, 0),
        total_trans_fat: todayRecords.reduce((sum, record) => sum + record.actual_trans_fat, 0),
        total_cholesterol: todayRecords.reduce((sum, record) => sum + record.actual_cholesterol, 0),
        total_sodium: todayRecords.reduce((sum, record) => sum + record.actual_sodium, 0),
        total_potassium: todayRecords.reduce((sum, record) => sum + record.actual_potassium, 0),
        total_dietary_fiber: todayRecords.reduce((sum, record) => sum + record.actual_dietary_fiber, 0),
        total_sugar: todayRecords.reduce((sum, record) => sum + record.actual_sugar, 0),
        total_vitamin_a: todayRecords.reduce((sum, record) => sum + record.actual_vitamin_a, 0),
        total_vitamin_c: todayRecords.reduce((sum, record) => sum + record.actual_vitamin_c, 0),
        total_calcium: todayRecords.reduce((sum, record) => sum + record.actual_calcium, 0),
        total_iron: todayRecords.reduce((sum, record) => sum + record.actual_iron, 0)
      }
      
      nutritionStore.setDailySummaries([dailySummary])
      console.log('✅ 每日营养汇总已计算并初始化')
    }

    console.log('🎉 所有测试数据初始化完成！')
    
    return true
  } catch (error) {
    console.error('❌ 测试数据初始化失败:', error)
    return false
  }
}

/**
 * 清除所有测试数据
 */
export function clearMockData() {
  const foodStore = useFoodStore()
  const nutritionStore = useNutritionStore()
  const userStore = useUserStore()

  try {
    foodStore.setTags([])
    foodStore.setFoods([])
    nutritionStore.setRecords([])
    nutritionStore.setDailySummaries([])
    userStore.setUser(null)
    
    console.log('🧹 所有测试数据已清除')
    return true
  } catch (error) {
    console.error('❌ 清除测试数据失败:', error)
    return false
  }
}

/**
 * 检查是否已有数据
 */
export function hasExistingData() {
  const foodStore = useFoodStore()
  const nutritionStore = useNutritionStore()
  const userStore = useUserStore()

  return (
    foodStore.foods.length > 0 ||
    nutritionStore.records.length > 0 ||
    userStore.user !== null
  )
}

/**
 * 安全初始化测试数据（仅在没有现有数据时）
 */
export function safeInitMockData() {
  if (!hasExistingData()) {
    return initMockData()
  } else {
    console.log('ℹ️ 检测到现有数据，跳过测试数据初始化')
    return false
  }
}
