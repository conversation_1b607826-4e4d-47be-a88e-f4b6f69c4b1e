/**
 * NRV% (营养素参考值百分比) 计算工具
 * 基于中国营养学会推荐的成人每日营养素参考摄入量
 */

// NRV参考值常量 (成人每日推荐摄入量)
export const NRV_REFERENCE_VALUES = {
  calories: 2000, // 千卡
  carbohydrates: 300, // 克
  total_fat: 60, // 克
  protein: 60, // 克
  saturated_fat: 20, // 克
  cholesterol: 300, // 毫克
  sodium: 2000, // 毫克
  potassium: 3500, // 毫克
  dietary_fiber: 25, // 克
  vitamin_a: 800, // 微克RE
  vitamin_c: 100, // 毫克
  calcium: 800, // 毫克
  iron: 15, // 毫克
} as const

export type NutrientKey = keyof typeof NRV_REFERENCE_VALUES

/**
 * 计算单个营养素的NRV%
 * @param nutrientValue 营养素含量
 * @param nutrientKey 营养素类型
 * @returns NRV百分比，保留2位小数
 */
export function calculateNRV(nutrientValue: number, nutrientKey: NutrientKey): number {
  if (nutrientValue <= 0) return 0
  
  const referenceValue = NRV_REFERENCE_VALUES[nutrientKey]
  if (!referenceValue) return 0
  
  const nrvPercentage = (nutrientValue / referenceValue) * 100
  return Math.round(nrvPercentage * 100) / 100 // 保留2位小数
}

/**
 * 批量计算营养成分的NRV%
 * @param nutritionData 营养成分数据对象
 * @returns 包含NRV%的对象
 */
export function calculateAllNRV(nutritionData: {
  calories?: number
  carbohydrates?: number
  total_fat?: number
  protein?: number
  saturated_fat?: number
  cholesterol?: number
  sodium?: number
  potassium?: number
  dietary_fiber?: number
  vitamin_a?: number
  vitamin_c?: number
  calcium?: number
  iron?: number
}) {
  const nrvData: Record<string, number> = {}
  
  // 遍历所有营养素并计算NRV%
  Object.entries(nutritionData).forEach(([key, value]) => {
    if (value !== undefined && value > 0 && key in NRV_REFERENCE_VALUES) {
      const nrvKey = `${key}_nrv`
      nrvData[nrvKey] = calculateNRV(value, key as NutrientKey)
    }
  })
  
  return nrvData
}

/**
 * 根据NRV%反推营养素含量
 * @param nrvPercentage NRV百分比
 * @param nutrientKey 营养素类型
 * @returns 营养素含量
 */
export function calculateNutrientFromNRV(nrvPercentage: number, nutrientKey: NutrientKey): number {
  if (nrvPercentage <= 0) return 0
  
  const referenceValue = NRV_REFERENCE_VALUES[nutrientKey]
  if (!referenceValue) return 0
  
  const nutrientValue = (nrvPercentage / 100) * referenceValue
  return Math.round(nutrientValue * 100) / 100 // 保留2位小数
}

/**
 * 获取营养素的参考值和单位信息
 * @param nutrientKey 营养素类型
 * @returns 参考值信息
 */
export function getNutrientInfo(nutrientKey: NutrientKey) {
  const referenceValue = NRV_REFERENCE_VALUES[nutrientKey]
  
  // 单位映射
  const unitMap: Record<NutrientKey, string> = {
    calories: '千卡',
    carbohydrates: '克',
    total_fat: '克',
    protein: '克',
    saturated_fat: '克',
    cholesterol: '毫克',
    sodium: '毫克',
    potassium: '毫克',
    dietary_fiber: '克',
    vitamin_a: '微克RE',
    vitamin_c: '毫克',
    calcium: '毫克',
    iron: '毫克',
  }
  
  // 中文名称映射
  const nameMap: Record<NutrientKey, string> = {
    calories: '能量',
    carbohydrates: '碳水化合物',
    total_fat: '脂肪',
    protein: '蛋白质',
    saturated_fat: '饱和脂肪',
    cholesterol: '胆固醇',
    sodium: '钠',
    potassium: '钾',
    dietary_fiber: '膳食纤维',
    vitamin_a: '维生素A',
    vitamin_c: '维生素C',
    calcium: '钙',
    iron: '铁',
  }
  
  return {
    key: nutrientKey,
    name: nameMap[nutrientKey],
    referenceValue,
    unit: unitMap[nutrientKey],
    description: `成人每日${nameMap[nutrientKey]}参考摄入量`
  }
}

/**
 * 验证NRV%值是否合理
 * @param nrvPercentage NRV百分比
 * @param nutrientKey 营养素类型
 * @returns 验证结果和建议
 */
export function validateNRV(nrvPercentage: number, nutrientKey: NutrientKey) {
  if (nrvPercentage < 0) {
    return {
      isValid: false,
      message: 'NRV%不能为负数'
    }
  }
  
  if (nrvPercentage > 1000) {
    return {
      isValid: false,
      message: 'NRV%过高，请检查数值是否正确'
    }
  }
  
  // 特殊营养素的合理性检查
  const warnings: Record<string, number> = {
    sodium: 50, // 钠含量过高警告阈值
    saturated_fat: 25, // 饱和脂肪过高警告阈值
    cholesterol: 33, // 胆固醇过高警告阈值
  }
  
  if (nutrientKey in warnings && nrvPercentage > warnings[nutrientKey]) {
    return {
      isValid: true,
      message: `${getNutrientInfo(nutrientKey).name}含量较高，建议适量摄入`,
      isWarning: true
    }
  }
  
  return {
    isValid: true,
    message: ''
  }
}

/**
 * 格式化NRV%显示
 * @param nrvPercentage NRV百分比
 * @returns 格式化后的字符串
 */
export function formatNRV(nrvPercentage: number): string {
  if (nrvPercentage === 0) return '0%'
  if (nrvPercentage < 0.01) return '<0.01%'
  if (nrvPercentage >= 1000) return '≥1000%'
  
  return `${nrvPercentage.toFixed(2)}%`
}
