// 测试日期修复功能
import dayjs from 'dayjs'

export const testDateFix = () => {
  console.log('🧪 测试日期修复功能...')
  
  try {
    // 测试正常日期
    const normalDate = Date.now()
    const normalDateStr = dayjs(normalDate).format('YYYY-MM-DD')
    console.log('✅ 正常日期处理:', normalDateStr)
    
    // 测试null值处理
    const nullDate = null
    const fallbackDate = nullDate || Date.now()
    const fallbackDateStr = dayjs(fallbackDate).format('YYYY-MM-DD')
    console.log('✅ null值处理:', fallbackDateStr)
    
    // 测试无效日期处理
    const invalidDate = 'invalid-date'
    const parsedDate = dayjs(invalidDate)
    if (parsedDate.isValid()) {
      console.log('✅ 有效日期:', parsedDate.format('YYYY-MM-DD'))
    } else {
      const validDate = dayjs().format('YYYY-MM-DD')
      console.log('✅ 无效日期处理，使用当前日期:', validDate)
    }
    
    // 测试时间处理
    const timeStr = '14:30'
    const timeValue = dayjs(timeStr, 'HH:mm')
    if (timeValue.isValid()) {
      console.log('✅ 时间处理:', timeValue.format('HH:mm'))
    } else {
      console.log('❌ 时间处理失败')
    }
    
    console.log('🎉 日期修复功能测试通过！')
    return true
    
  } catch (error) {
    console.error('❌ 日期修复测试失败:', error)
    return false
  }
}

// 在开发环境下自动运行测试
if (import.meta.env.DEV) {
  setTimeout(() => {
    testDateFix()
  }, 3000)
}
