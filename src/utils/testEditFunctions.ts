// 测试编辑功能的工具函数
import { useFoodStore } from '../stores/food'
import { useNutritionStore } from '../stores/nutrition'

export const testEditFunctions = () => {
  const foodStore = useFoodStore()
  const nutritionStore = useNutritionStore()

  // 测试食物编辑功能
  const testFoodEdit = () => {
    console.log('测试食物编辑功能...')
    
    // 创建测试食物
    const testFood = {
      id: 999,
      name: '测试食物',
      brand: '测试品牌',
      barcode: '1234567890',
      user_id: 1,
      is_public: false,
      calories: 100,
      carbohydrates: 20,
      total_fat: 5,
      protein: 10,
      saturated_fat: 2,
      trans_fat: 0,
      cholesterol: 0,
      sodium: 100,
      potassium: 200,
      dietary_fiber: 3,
      sugar: 5,
      vitamin_a: 0,
      vitamin_c: 10,
      calcium: 50,
      iron: 2,
      calories_nrv: 5,
      carbohydrates_nrv: 7,
      total_fat_nrv: 8,
      protein_nrv: 17,
      saturated_fat_nrv: 10,
      cholesterol_nrv: 0,
      sodium_nrv: 5,
      potassium_nrv: 6,
      dietary_fiber_nrv: 12,
      vitamin_a_nrv: 0,
      vitamin_c_nrv: 10,
      calcium_nrv: 6,
      iron_nrv: 13,
      description: '这是一个测试食物'
    }

    // 添加到store
    foodStore.addFood(testFood)
    console.log('测试食物已添加:', testFood)

    // 测试更新
    const updates = {
      name: '更新后的测试食物',
      calories: 150,
      protein: 15
    }
    
    foodStore.updateFood(999, updates)
    console.log('食物更新完成:', updates)

    // 验证更新
    const updatedFood = foodStore.foods.find(f => f.id === 999)
    console.log('更新后的食物:', updatedFood)

    return updatedFood
  }

  // 测试营养记录编辑功能
  const testRecordEdit = () => {
    console.log('测试营养记录编辑功能...')
    
    // 创建测试记录
    const testRecord = {
      id: 999,
      user_id: 1,
      record_date: '2024-01-15',
      record_time: '12:00',
      food_id: 999,
      quantity: 100,
      unit: '克',
      actual_calories: 100,
      actual_carbohydrates: 20,
      actual_total_fat: 5,
      actual_protein: 10,
      actual_saturated_fat: 2,
      actual_trans_fat: 0,
      actual_cholesterol: 0,
      actual_sodium: 100,
      actual_potassium: 200,
      actual_dietary_fiber: 3,
      actual_sugar: 5,
      actual_vitamin_a: 0,
      actual_vitamin_c: 10,
      actual_calcium: 50,
      actual_iron: 2,
      notes: '测试记录'
    }

    // 添加到store
    nutritionStore.addRecord(testRecord)
    console.log('测试记录已添加:', testRecord)

    // 测试更新
    const updates = {
      quantity: 150,
      actual_calories: 150,
      actual_protein: 15,
      notes: '更新后的测试记录'
    }
    
    nutritionStore.updateRecord(999, updates)
    console.log('记录更新完成:', updates)

    // 验证更新
    const updatedRecord = nutritionStore.records.find(r => r.id === 999)
    console.log('更新后的记录:', updatedRecord)

    return updatedRecord
  }

  // 清理测试数据
  const cleanupTestData = () => {
    console.log('清理测试数据...')
    foodStore.deleteFood(999)
    nutritionStore.deleteRecord(999)
    console.log('测试数据已清理')
  }

  return {
    testFoodEdit,
    testRecordEdit,
    cleanupTestData
  }
}

// 在浏览器控制台中可以使用的全局测试函数
if (typeof window !== 'undefined') {
  (window as any).testEditFunctions = testEditFunctions
}
