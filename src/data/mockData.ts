import type { Food, FoodTag } from '../stores/food'
import type { NutritionRecord } from '../stores/nutrition'
import type { User } from '../stores/user'

// 扩展营养成分的 JSON 格式定义
export interface ExtendedNutrition {
  // 维生素类
  vitamin_d?: number // 维生素D (IU)
  vitamin_e?: number // 维生素E (mg)
  vitamin_k?: number // 维生素K (mcg)
  vitamin_b1?: number // 维生素B1/硫胺素 (mg)
  vitamin_b2?: number // 维生素B2/核黄素 (mg)
  vitamin_b3?: number // 维生素B3/烟酸 (mg)
  vitamin_b6?: number // 维生素B6 (mg)
  vitamin_b12?: number // 维生素B12 (mcg)
  folate?: number // 叶酸 (mcg)
  biotin?: number // 生物素 (mcg)
  pantothenic_acid?: number // 泛酸 (mg)
  
  // 矿物质类
  magnesium?: number // 镁 (mg)
  phosphorus?: number // 磷 (mg)
  zinc?: number // 锌 (mg)
  copper?: number // 铜 (mg)
  manganese?: number // 锰 (mg)
  selenium?: number // 硒 (mcg)
  iodine?: number // 碘 (mcg)
  chromium?: number // 铬 (mcg)
  molybdenum?: number // 钼 (mcg)
  
  // 脂肪酸类
  omega_3?: number // Omega-3脂肪酸 (g)
  omega_6?: number // Omega-6脂肪酸 (g)
  dha?: number // DHA (mg)
  epa?: number // EPA (mg)
  
  // 氨基酸类
  leucine?: number // 亮氨酸 (mg)
  isoleucine?: number // 异亮氨酸 (mg)
  valine?: number // 缬氨酸 (mg)
  lysine?: number // 赖氨酸 (mg)
  methionine?: number // 蛋氨酸 (mg)
  phenylalanine?: number // 苯丙氨酸 (mg)
  threonine?: number // 苏氨酸 (mg)
  tryptophan?: number // 色氨酸 (mg)
  histidine?: number // 组氨酸 (mg)
  
  // 其他营养素
  caffeine?: number // 咖啡因 (mg)
  alcohol?: number // 酒精 (g)
  water?: number // 水分 (g)
  ash?: number // 灰分 (g)
}

// 食物标签测试数据
export const mockFoodTags: FoodTag[] = [
  { id: 1, name: '谷物', color: '#f39c12', is_system: true },
  { id: 2, name: '蔬菜', color: '#27ae60', is_system: true },
  { id: 3, name: '水果', color: '#e74c3c', is_system: true },
  { id: 4, name: '肉类', color: '#8e44ad', is_system: true },
  { id: 5, name: '海鲜', color: '#3498db', is_system: true },
  { id: 6, name: '乳制品', color: '#f1c40f', is_system: true },
  { id: 7, name: '坚果', color: '#d35400', is_system: true },
  { id: 8, name: '饮品', color: '#16a085', is_system: true },
  { id: 9, name: '零食', color: '#e67e22', is_system: false },
  { id: 10, name: '调料', color: '#95a5a6', is_system: true },
  { id: 11, name: '高蛋白', color: '#9b59b6', is_system: false },
  { id: 12, name: '低脂', color: '#1abc9c', is_system: false },
  { id: 13, name: '有机', color: '#2ecc71', is_system: false },
  { id: 14, name: '无糖', color: '#34495e', is_system: false }
]

// 食物测试数据
export const mockFoods: Food[] = [
  {
    id: 1,
    name: '白米饭',
    brand: '',
    barcode: '',
    calories: 130,
    carbohydrates: 28,
    total_fat: 0.3,
    protein: 2.7,
    saturated_fat: 0.1,
    trans_fat: 0,
    cholesterol: 0,
    sodium: 1,
    potassium: 35,
    dietary_fiber: 0.4,
    sugar: 0.1,
    vitamin_a: 0,
    vitamin_c: 0,
    calcium: 10,
    iron: 0.8,
    // NRV% 字段
    calories_nrv: 6.5, // 130/2000*100
    carbohydrates_nrv: 9.3, // 28/300*100
    total_fat_nrv: 0.5, // 0.3/60*100
    protein_nrv: 4.5, // 2.7/60*100
    saturated_fat_nrv: 0.5, // 0.1/20*100
    cholesterol_nrv: 0, // 0/300*100
    sodium_nrv: 0.05, // 1/2000*100
    potassium_nrv: 1.0, // 35/3500*100
    dietary_fiber_nrv: 1.6, // 0.4/25*100
    vitamin_a_nrv: 0, // 0/800*100
    vitamin_c_nrv: 0, // 0/100*100
    calcium_nrv: 1.25, // 10/800*100
    iron_nrv: 5.33, // 0.8/15*100
    serving_size: 100,
    serving_unit: 'g',
    description: '优质大米制作的白米饭',
    is_public: true,
    user_id: 1,
    tags: [{ id: 1, name: '谷物', color: '#f39c12', is_system: true }],
    extended_nutrition: {
      vitamin_b1: 0.07,
      vitamin_b3: 1.6,
      magnesium: 25,
      phosphorus: 68,
      zinc: 1.1,
      water: 68.4
    }
  },
  {
    id: 2,
    name: '鸡胸肉',
    brand: '',
    barcode: '',
    calories: 165,
    carbohydrates: 0,
    total_fat: 3.6,
    protein: 31,
    saturated_fat: 1.0,
    trans_fat: 0,
    cholesterol: 85,
    sodium: 74,
    potassium: 256,
    dietary_fiber: 0,
    sugar: 0,
    vitamin_a: 21,
    vitamin_c: 1.2,
    calcium: 15,
    iron: 1.0,
    // NRV% 字段
    calories_nrv: 8.25, // 165/2000*100
    carbohydrates_nrv: 0, // 0/300*100
    total_fat_nrv: 6.0, // 3.6/60*100
    protein_nrv: 51.7, // 31/60*100
    saturated_fat_nrv: 5.0, // 1.0/20*100
    cholesterol_nrv: 28.3, // 85/300*100
    sodium_nrv: 3.7, // 74/2000*100
    potassium_nrv: 7.3, // 256/3500*100
    dietary_fiber_nrv: 0, // 0/25*100
    vitamin_a_nrv: 2.63, // 21/800*100
    vitamin_c_nrv: 1.2, // 1.2/100*100
    calcium_nrv: 1.88, // 15/800*100
    iron_nrv: 6.67, // 1.0/15*100
    serving_size: 100,
    serving_unit: 'g',
    description: '去皮鸡胸肉，高蛋白低脂肪',
    is_public: true,
    user_id: 1,
    tags: [
      { id: 4, name: '肉类', color: '#8e44ad', is_system: true },
      { id: 11, name: '高蛋白', color: '#9b59b6', is_system: false },
      { id: 12, name: '低脂', color: '#1abc9c', is_system: false }
    ],
    extended_nutrition: {
      vitamin_b3: 10.9,
      vitamin_b6: 0.5,
      vitamin_b12: 0.3,
      phosphorus: 196,
      selenium: 22.0,
      leucine: 1761,
      isoleucine: 1128,
      valine: 1157
    }
  },
  {
    id: 3,
    name: '西兰花',
    brand: '',
    barcode: '',
    calories: 34,
    carbohydrates: 7,
    total_fat: 0.4,
    protein: 2.8,
    saturated_fat: 0.1,
    trans_fat: 0,
    cholesterol: 0,
    sodium: 33,
    potassium: 316,
    dietary_fiber: 2.6,
    sugar: 1.5,
    vitamin_a: 623,
    vitamin_c: 89.2,
    calcium: 47,
    iron: 0.7,
    serving_size: 100,
    serving_unit: 'g',
    description: '新鲜西兰花，富含维生素C',
    is_public: true,
    user_id: 1,
    tags: [
      { id: 2, name: '蔬菜', color: '#27ae60', is_system: true },
      { id: 13, name: '有机', color: '#2ecc71', is_system: false }
    ],
    extended_nutrition: {
      vitamin_k: 101.6,
      folate: 63,
      vitamin_e: 0.78,
      magnesium: 21,
      phosphorus: 66,
      zinc: 0.41,
      water: 89.3
    }
  },
  {
    id: 4,
    name: '香蕉',
    brand: '',
    barcode: '',
    calories: 89,
    carbohydrates: 23,
    total_fat: 0.3,
    protein: 1.1,
    saturated_fat: 0.1,
    trans_fat: 0,
    cholesterol: 0,
    sodium: 1,
    potassium: 358,
    dietary_fiber: 2.6,
    sugar: 12.2,
    vitamin_a: 64,
    vitamin_c: 8.7,
    calcium: 5,
    iron: 0.3,
    serving_size: 100,
    serving_unit: 'g',
    description: '新鲜香蕉，富含钾元素',
    is_public: true,
    user_id: 1,
    tags: [{ id: 3, name: '水果', color: '#e74c3c', is_system: true }],
    extended_nutrition: {
      vitamin_b6: 0.4,
      folate: 20,
      magnesium: 27,
      phosphorus: 22,
      water: 74.9
    }
  },
  {
    id: 5,
    name: '三文鱼',
    brand: '',
    barcode: '',
    calories: 208,
    carbohydrates: 0,
    total_fat: 12.4,
    protein: 22.1,
    saturated_fat: 3.1,
    trans_fat: 0,
    cholesterol: 59,
    sodium: 59,
    potassium: 363,
    dietary_fiber: 0,
    sugar: 0,
    vitamin_a: 58,
    vitamin_c: 3.9,
    calcium: 9,
    iron: 0.3,
    serving_size: 100,
    serving_unit: 'g',
    description: '新鲜三文鱼，富含Omega-3脂肪酸',
    is_public: true,
    user_id: 1,
    tags: [
      { id: 5, name: '海鲜', color: '#3498db', is_system: true },
      { id: 11, name: '高蛋白', color: '#9b59b6', is_system: false }
    ],
    extended_nutrition: {
      vitamin_d: 360,
      vitamin_b12: 3.2,
      omega_3: 1.8,
      dha: 1104,
      epa: 690,
      selenium: 36.5,
      phosphorus: 200
    }
  }
]

// 用户测试数据
export const mockUser: User = {
  id: 1,
  username: 'demo_user',
  email: '<EMAIL>',
  nickname: '张三',
  avatar_url: '',
  birth_date: '1990-05-15',
  gender: 'male',
  height: 175,
  weight: 70,
  activity_level: 'moderate'
}

// 营养记录测试数据
export const mockNutritionRecords: NutritionRecord[] = [
  {
    id: 1,
    user_id: 1,
    record_date: '2024-01-15',
    record_time: '08:30',
    food_id: 1,
    food: mockFoods[0],
    quantity: 150,
    unit: 'g',
    actual_calories: 195,
    actual_carbohydrates: 42,
    actual_total_fat: 0.45,
    actual_protein: 4.05,
    actual_saturated_fat: 0.15,
    actual_trans_fat: 0,
    actual_cholesterol: 0,
    actual_sodium: 1.5,
    actual_potassium: 52.5,
    actual_dietary_fiber: 0.6,
    actual_sugar: 0.15,
    actual_vitamin_a: 0,
    actual_vitamin_c: 0,
    actual_calcium: 15,
    actual_iron: 1.2,
    notes: '早餐主食'
  },
  {
    id: 2,
    user_id: 1,
    record_date: '2024-01-15',
    record_time: '12:15',
    food_id: 2,
    food: mockFoods[1],
    quantity: 120,
    unit: 'g',
    actual_calories: 198,
    actual_carbohydrates: 0,
    actual_total_fat: 4.32,
    actual_protein: 37.2,
    actual_saturated_fat: 1.2,
    actual_trans_fat: 0,
    actual_cholesterol: 102,
    actual_sodium: 88.8,
    actual_potassium: 307.2,
    actual_dietary_fiber: 0,
    actual_sugar: 0,
    actual_vitamin_a: 25.2,
    actual_vitamin_c: 1.44,
    actual_calcium: 18,
    actual_iron: 1.2,
    notes: '午餐蛋白质'
  },
  {
    id: 3,
    user_id: 1,
    record_date: '2024-01-15',
    record_time: '15:30',
    food_name: '自制蛋白奶昔',
    quantity: 250,
    unit: 'ml',
    actual_calories: 180,
    actual_carbohydrates: 15,
    actual_total_fat: 3,
    actual_protein: 25,
    actual_saturated_fat: 1.5,
    actual_trans_fat: 0,
    actual_cholesterol: 10,
    actual_sodium: 120,
    actual_potassium: 300,
    actual_dietary_fiber: 2,
    actual_sugar: 12,
    actual_vitamin_a: 100,
    actual_vitamin_c: 5,
    actual_calcium: 200,
    actual_iron: 2,
    notes: '下午加餐，自制蛋白奶昔'
  }
]
