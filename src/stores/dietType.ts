import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 饮食类型接口定义
export interface DietType {
  id: number
  name: string
  nameEn?: string
  description: string
  nutritionFeatures: string
  benefits: string
  suitablePeople: string
  imageUrl?: string
  color: string
  isSystem: boolean
  userId?: number
  createdAt: string
  updatedAt: string
}

// 饮食类型食物分类接口定义
export interface DietTypeFoodCategory {
  id: number
  dietTypeId: number
  categoryName: string
  categoryNameEn?: string
  description: string
  examples: string
  recommendedAmount: string
  sortOrder: number
  createdAt: string
}

export const useDietTypeStore = defineStore('dietType', () => {
  // 状态
  const dietTypes = ref<DietType[]>([])
  const foodCategories = ref<DietTypeFoodCategory[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const systemDietTypes = computed(() => 
    dietTypes.value.filter(type => type.isSystem)
  )

  const userDietTypes = computed(() => 
    dietTypes.value.filter(type => !type.isSystem)
  )

  // 根据饮食类型ID获取食物分类
  const getFoodCategoriesByDietType = computed(() => (dietTypeId: number) =>
    foodCategories.value
      .filter(category => category.dietTypeId === dietTypeId)
      .sort((a, b) => a.sortOrder - b.sortOrder)
  )

  // 根据ID获取饮食类型
  const getDietTypeById = computed(() => (id: number) =>
    dietTypes.value.find(type => type.id === id)
  )

  // 动作
  const fetchDietTypes = async () => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 模拟数据
      dietTypes.value = [
        {
          id: 1,
          name: '地中海饮食',
          nameEn: 'Mediterranean Diet',
          description: '地中海饮食是一种基于地中海沿岸国家传统饮食模式的健康饮食方式，强调新鲜蔬果、全谷物、豆类、坚果、橄榄油和适量鱼类的摄入。',
          nutritionFeatures: '富含单不饱和脂肪酸、膳食纤维、抗氧化物质；低饱和脂肪；适量蛋白质',
          benefits: '降低心血管疾病风险、改善认知功能、抗炎、延缓衰老、有助于体重管理',
          suitablePeople: '心血管疾病患者、糖尿病患者、需要减重的人群、中老年人群',
          color: '#52c41a',
          isSystem: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 2,
          name: 'DASH饮食',
          nameEn: 'DASH Diet',
          description: 'DASH饮食（Dietary Approaches to Stop Hypertension）是专门为降低血压而设计的饮食模式，强调低钠、高钾、高纤维的食物选择。',
          nutritionFeatures: '低钠高钾、富含膳食纤维、适量蛋白质、低饱和脂肪',
          benefits: '降低血压、改善心血管健康、预防中风、有助于体重控制',
          suitablePeople: '高血压患者、心血管疾病高危人群、需要控制体重的人群',
          color: '#1890ff',
          isSystem: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 3,
          name: '植物性饮食',
          nameEn: 'Plant-Based Diet',
          description: '植物性饮食以植物来源的食物为主，包括蔬菜、水果、全谷物、豆类、坚果和种子，限制或避免动物产品。',
          nutritionFeatures: '高膳食纤维、富含植物化学物质、低饱和脂肪、零胆固醇',
          benefits: '降低慢性疾病风险、环保可持续、改善消化健康、有助于体重管理',
          suitablePeople: '素食主义者、环保主义者、消化系统敏感人群、需要减重的人群',
          color: '#722ed1',
          isSystem: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 4,
          name: '低碳水化合物饮食',
          nameEn: 'Low-Carb Diet',
          description: '低碳水化合物饮食限制碳水化合物的摄入，增加蛋白质和健康脂肪的比例，促进身体燃烧脂肪获取能量。',
          nutritionFeatures: '低碳水化合物、高蛋白质、适量健康脂肪',
          benefits: '快速减重、改善血糖控制、提高饱腹感、可能改善心血管指标',
          suitablePeople: '需要快速减重的人群、2型糖尿病患者、代谢综合征患者',
          color: '#fa8c16',
          isSystem: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 5,
          name: '日式饮食',
          nameEn: 'Japanese Diet',
          description: '日式饮食以米饭为主食，搭配鱼类、蔬菜、豆制品和海藻，注重食材的新鲜和营养平衡。',
          nutritionFeatures: '低脂肪、适量蛋白质、富含omega-3脂肪酸、高纤维',
          benefits: '长寿、降低心血管疾病风险、维持健康体重、改善肠道健康',
          suitablePeople: '追求长寿的人群、心血管疾病患者、需要控制体重的人群',
          color: '#13c2c2',
          isSystem: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 6,
          name: '传统中式饮食',
          nameEn: 'Traditional Chinese Diet',
          description: '传统中式饮食注重食物的平衡搭配，强调"药食同源"，以谷物为主，搭配蔬菜、豆类和适量肉类。',
          nutritionFeatures: '营养均衡、食材多样、注重食物性味、季节性搭配',
          benefits: '营养全面、易消化、符合中医养生理念、适应性强',
          suitablePeople: '中国人群、消化功能较弱的人群、追求传统养生的人群',
          color: '#eb2f96',
          isSystem: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      ]
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取饮食类型失败'
    } finally {
      loading.value = false
    }
  }

  const fetchFoodCategories = async () => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      
      // 模拟数据 - 地中海饮食的食物分类
      const mediterraneanCategories: DietTypeFoodCategory[] = [
        {
          id: 1,
          dietTypeId: 1,
          categoryName: '蔬菜',
          categoryNameEn: 'Vegetables',
          description: '新鲜的各类蔬菜，富含维生素、矿物质和膳食纤维',
          examples: '西红柿、黄瓜、菠菜、茄子、洋葱、胡萝卜',
          recommendedAmount: '每日400-500g',
          sortOrder: 1,
          createdAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 2,
          dietTypeId: 1,
          categoryName: '水果',
          categoryNameEn: 'Fruits',
          description: '新鲜的季节性水果，提供维生素C和抗氧化物质',
          examples: '橙子、苹果、葡萄、无花果、柠檬',
          recommendedAmount: '每日200-300g',
          sortOrder: 2,
          createdAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 3,
          dietTypeId: 1,
          categoryName: '全谷物',
          categoryNameEn: 'Whole Grains',
          description: '未精制的谷物，提供复合碳水化合物和B族维生素',
          examples: '全麦面包、糙米、燕麦、大麦',
          recommendedAmount: '每日150-200g',
          sortOrder: 3,
          createdAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 4,
          dietTypeId: 1,
          categoryName: '豆类',
          categoryNameEn: 'Legumes',
          description: '各种豆类，富含植物蛋白和膳食纤维',
          examples: '扁豆、鹰嘴豆、白豆、黑豆',
          recommendedAmount: '每周3-4次',
          sortOrder: 4,
          createdAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 5,
          dietTypeId: 1,
          categoryName: '坚果',
          categoryNameEn: 'Nuts',
          description: '各种坚果，提供健康脂肪和蛋白质',
          examples: '核桃、杏仁、榛子、松子',
          recommendedAmount: '每日30g',
          sortOrder: 5,
          createdAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 6,
          dietTypeId: 1,
          categoryName: '橄榄油',
          categoryNameEn: 'Olive Oil',
          description: '特级初榨橄榄油，主要的脂肪来源',
          examples: '特级初榨橄榄油',
          recommendedAmount: '每日2-3汤匙',
          sortOrder: 6,
          createdAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 7,
          dietTypeId: 1,
          categoryName: '鱼类',
          categoryNameEn: 'Fish',
          description: '富含omega-3脂肪酸的鱼类',
          examples: '三文鱼、沙丁鱼、鲭鱼、金枪鱼',
          recommendedAmount: '每周2-3次',
          sortOrder: 7,
          createdAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 8,
          dietTypeId: 1,
          categoryName: '红酒',
          categoryNameEn: 'Red Wine',
          description: '适量的红酒（可选）',
          examples: '干红葡萄酒',
          recommendedAmount: '每日1小杯（可选）',
          sortOrder: 8,
          createdAt: '2024-01-01T00:00:00Z'
        }
      ]

      foodCategories.value = mediterraneanCategories
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取食物分类失败'
    } finally {
      loading.value = false
    }
  }

  // 创建饮食类型
  const createDietType = async (dietType: Omit<DietType, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const newDietType: DietType = {
        ...dietType,
        id: Math.max(...dietTypes.value.map(d => d.id)) + 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      dietTypes.value.push(newDietType)
      return newDietType
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建饮食类型失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新饮食类型
  const updateDietType = async (id: number, updates: Partial<DietType>) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const index = dietTypes.value.findIndex(d => d.id === id)
      if (index === -1) {
        throw new Error('饮食类型不存在')
      }
      
      dietTypes.value[index] = {
        ...dietTypes.value[index],
        ...updates,
        updatedAt: new Date().toISOString()
      }
      
      return dietTypes.value[index]
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新饮食类型失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 删除饮食类型
  const deleteDietType = async (id: number) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const index = dietTypes.value.findIndex(d => d.id === id)
      if (index === -1) {
        throw new Error('饮食类型不存在')
      }
      
      // 不能删除系统预设类型
      if (dietTypes.value[index].isSystem) {
        throw new Error('不能删除系统预设饮食类型')
      }
      
      dietTypes.value.splice(index, 1)
      
      // 同时删除相关的食物分类
      foodCategories.value = foodCategories.value.filter(c => c.dietTypeId !== id)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除饮食类型失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 创建食物分类
  const createFoodCategory = async (category: Omit<DietTypeFoodCategory, 'id' | 'createdAt'>) => {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))

      const newCategory: DietTypeFoodCategory = {
        ...category,
        id: Math.max(...foodCategories.value.map(c => c.id), 0) + 1,
        createdAt: new Date().toISOString()
      }

      foodCategories.value.push(newCategory)
      return newCategory
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建食物分类失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新食物分类
  const updateFoodCategory = async (id: number, updates: Partial<DietTypeFoodCategory>) => {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))

      const index = foodCategories.value.findIndex(c => c.id === id)
      if (index === -1) {
        throw new Error('食物分类不存在')
      }

      foodCategories.value[index] = {
        ...foodCategories.value[index],
        ...updates
      }

      return foodCategories.value[index]
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新食物分类失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 删除食物分类
  const deleteFoodCategory = async (id: number) => {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))

      const index = foodCategories.value.findIndex(c => c.id === id)
      if (index === -1) {
        throw new Error('食物分类不存在')
      }

      foodCategories.value.splice(index, 1)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除食物分类失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 获取指定饮食类型的食物分类（用于编辑）
  const getFoodCategoryById = computed(() => (id: number) =>
    foodCategories.value.find(category => category.id === id)
  )

  return {
    // 状态
    dietTypes,
    foodCategories,
    loading,
    error,

    // 计算属性
    systemDietTypes,
    userDietTypes,
    getFoodCategoriesByDietType,
    getDietTypeById,
    getFoodCategoryById,

    // 动作
    fetchDietTypes,
    fetchFoodCategories,
    createDietType,
    updateDietType,
    deleteDietType,
    createFoodCategory,
    updateFoodCategory,
    deleteFoodCategory
  }
})
