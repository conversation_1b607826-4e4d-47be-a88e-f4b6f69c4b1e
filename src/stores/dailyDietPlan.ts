import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 每日饮食计划接口定义
export interface DailyDietPlan {
  id: number
  userId: number
  planDate: string
  dietTypeId?: number
  planName?: string
  notes?: string
  createdAt: string
  updatedAt: string
}

// 每日饮食计划项目接口定义
export interface DailyDietPlanItem {
  id: number
  dailyDietPlanId: number
  categoryName: string
  categoryDescription?: string
  specificFoods?: SpecificFood[]
  isConsumed: boolean
  consumedAt?: string
  notes?: string
  sortOrder: number
  createdAt: string
  updatedAt: string
}

// 具体食物接口定义
export interface SpecificFood {
  id: string
  name: string
  description?: string
  amount?: string
  unit?: string
}

// 辅助函数：获取饮食类型名称
const getDietTypeName = (dietTypeId: number): string => {
  const dietTypeNames: { [key: number]: string } = {
    1: '地中海饮食',
    2: 'DASH饮食',
    3: '植物性饮食',
    4: '低碳水化合物饮食',
    5: '日式饮食',
    6: '传统中式饮食'
  }
  return dietTypeNames[dietTypeId] || '自定义饮食'
}

export const useDailyDietPlanStore = defineStore('dailyDietPlan', () => {
  // 状态
  const dailyPlans = ref<DailyDietPlan[]>([])
  const planItems = ref<DailyDietPlanItem[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const getPlanByDate = computed(() => (date: string) =>
    dailyPlans.value.find(plan => plan.planDate === date)
  )

  const getPlanItemsByPlanId = computed(() => (planId: number) =>
    planItems.value
      .filter(item => item.dailyDietPlanId === planId)
      .sort((a, b) => a.sortOrder - b.sortOrder)
  )

  const getTodayPlan = computed(() => {
    const today = new Date().toISOString().split('T')[0]
    return getPlanByDate.value(today)
  })

  const getTodayPlanItems = computed(() => {
    const todayPlan = getTodayPlan.value
    return todayPlan ? getPlanItemsByPlanId.value(todayPlan.id) : []
  })

  const getConsumedItemsCount = computed(() => (planId: number) => {
    const items = getPlanItemsByPlanId.value(planId)
    return items.filter(item => item.isConsumed).length
  })

  const getTotalItemsCount = computed(() => (planId: number) => {
    return getPlanItemsByPlanId.value(planId).length
  })

  const getCompletionRate = computed(() => (planId: number) => {
    const total = getTotalItemsCount.value(planId)
    if (total === 0) return 0
    const consumed = getConsumedItemsCount.value(planId)
    return Math.round((consumed / total) * 100)
  })

  // 动作
  const fetchDailyPlans = async (userId: number, startDate?: string, endDate?: string) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 模拟数据 - 生成最近30天的计划
      const today = new Date()
      const plans: DailyDietPlan[] = []

      for (let i = 0; i < 30; i++) {
        const planDate = new Date(today)
        planDate.setDate(today.getDate() - i)
        const dateString = planDate.toISOString().split('T')[0]

        // 随机生成一些计划（70%的概率有计划）
        if (Math.random() > 0.3) {
          const dietTypes = [1, 2, 3, 4, 5, 6] // 对应数据库中的饮食类型
          const randomDietType = dietTypes[Math.floor(Math.random() * dietTypes.length)]

          plans.push({
            id: i + 1,
            userId: 1,
            planDate: dateString,
            dietTypeId: randomDietType,
            planName: `${getDietTypeName(randomDietType)}计划`,
            notes: i === 0 ? '今天开始尝试新的饮食计划' : `第${i + 1}天的饮食计划`,
            createdAt: planDate.toISOString(),
            updatedAt: planDate.toISOString()
          })
        }
      }

      dailyPlans.value = plans
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取饮食计划失败'
    } finally {
      loading.value = false
    }
  }

  const fetchPlanItems = async (planId: number) => {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))

      // 如果已经有该计划的项目，直接返回
      const existingItems = planItems.value.filter(item => item.dailyDietPlanId === planId)
      if (existingItems.length > 0) {
        loading.value = false
        return
      }
      
      // 生成动态模拟数据
      const categories = [
        { name: '蔬菜', description: '新鲜的各类蔬菜，富含维生素、矿物质和膳食纤维' },
        { name: '水果', description: '新鲜的季节性水果，提供维生素C和抗氧化物质' },
        { name: '全谷物', description: '未精制的谷物，提供复合碳水化合物和B族维生素' },
        { name: '蛋白质', description: '优质蛋白质来源' },
        { name: '健康脂肪', description: '单不饱和和多不饱和脂肪' }
      ]

      const mockItems: DailyDietPlanItem[] = categories.map((category, index) => {
        const itemId = planId * 100 + index + 1 // 确保ID唯一
        const isConsumed = Math.random() > 0.4 // 60%的概率已摄入

        return {
          id: itemId,
          dailyDietPlanId: planId,
          categoryName: category.name,
          categoryDescription: category.description,
          specificFoods: [
            {
              id: `${itemId}-1`,
              name: `${category.name}食物示例`,
              amount: '100',
              unit: 'g'
            }
          ],
          isConsumed,
          consumedAt: isConsumed ? new Date().toISOString() : undefined,
          sortOrder: index + 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      })
      
      // 添加到现有项目中
      mockItems.forEach(newItem => {
        const existingIndex = planItems.value.findIndex(item => item.id === newItem.id)
        if (existingIndex >= 0) {
          planItems.value[existingIndex] = newItem
        } else {
          planItems.value.push(newItem)
        }
      })
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取计划项目失败'
    } finally {
      loading.value = false
    }
  }

  // 创建每日饮食计划
  const createDailyPlan = async (plan: Omit<DailyDietPlan, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const newPlan: DailyDietPlan = {
        ...plan,
        id: Math.max(...dailyPlans.value.map(p => p.id), 0) + 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      dailyPlans.value.push(newPlan)
      return newPlan
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建饮食计划失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新每日饮食计划
  const updateDailyPlan = async (id: number, updates: Partial<DailyDietPlan>) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const index = dailyPlans.value.findIndex(p => p.id === id)
      if (index === -1) {
        throw new Error('饮食计划不存在')
      }
      
      dailyPlans.value[index] = {
        ...dailyPlans.value[index],
        ...updates,
        updatedAt: new Date().toISOString()
      }
      
      return dailyPlans.value[index]
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新饮食计划失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 标记项目为已摄入/未摄入
  const toggleItemConsumption = async (itemId: number, isConsumed: boolean) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const index = planItems.value.findIndex(item => item.id === itemId)
      if (index === -1) {
        throw new Error('计划项目不存在')
      }
      
      planItems.value[index] = {
        ...planItems.value[index],
        isConsumed,
        consumedAt: isConsumed ? new Date().toISOString() : undefined,
        updatedAt: new Date().toISOString()
      }
      
      return planItems.value[index]
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新摄入状态失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 添加具体食物到计划项目
  const addSpecificFoodToItem = async (itemId: number, food: SpecificFood) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const index = planItems.value.findIndex(item => item.id === itemId)
      if (index === -1) {
        throw new Error('计划项目不存在')
      }
      
      const currentFoods = planItems.value[index].specificFoods || []
      planItems.value[index] = {
        ...planItems.value[index],
        specificFoods: [...currentFoods, food],
        updatedAt: new Date().toISOString()
      }
      
      return planItems.value[index]
    } catch (err) {
      error.value = err instanceof Error ? err.message : '添加具体食物失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 删除具体食物
  const removeSpecificFoodFromItem = async (itemId: number, foodId: string) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const index = planItems.value.findIndex(item => item.id === itemId)
      if (index === -1) {
        throw new Error('计划项目不存在')
      }
      
      const currentFoods = planItems.value[index].specificFoods || []
      planItems.value[index] = {
        ...planItems.value[index],
        specificFoods: currentFoods.filter(food => food.id !== foodId),
        updatedAt: new Date().toISOString()
      }
      
      return planItems.value[index]
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除具体食物失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    dailyPlans,
    planItems,
    loading,
    error,
    
    // 计算属性
    getPlanByDate,
    getPlanItemsByPlanId,
    getTodayPlan,
    getTodayPlanItems,
    getConsumedItemsCount,
    getTotalItemsCount,
    getCompletionRate,
    
    // 动作
    fetchDailyPlans,
    fetchPlanItems,
    createDailyPlan,
    updateDailyPlan,
    toggleItemConsumption,
    addSpecificFoodToItem,
    removeSpecificFoodFromItem
  }
})
