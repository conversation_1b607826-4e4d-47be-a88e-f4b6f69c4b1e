import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 每日饮食计划接口定义
export interface DailyDietPlan {
  id: number
  userId: number
  planDate: string
  dietTypeId?: number
  planName?: string
  notes?: string
  createdAt: string
  updatedAt: string
}

// 每日饮食计划项目接口定义
export interface DailyDietPlanItem {
  id: number
  dailyDietPlanId: number
  categoryName: string
  categoryDescription?: string
  specificFoods?: SpecificFood[]
  isConsumed: boolean
  consumedAt?: string
  notes?: string
  sortOrder: number
  createdAt: string
  updatedAt: string
}

// 具体食物接口定义
export interface SpecificFood {
  id: string
  name: string
  description?: string
  amount?: string
  unit?: string
}

export const useDailyDietPlanStore = defineStore('dailyDietPlan', () => {
  // 状态
  const dailyPlans = ref<DailyDietPlan[]>([])
  const planItems = ref<DailyDietPlanItem[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const getPlanByDate = computed(() => (date: string) =>
    dailyPlans.value.find(plan => plan.planDate === date)
  )

  const getPlanItemsByPlanId = computed(() => (planId: number) =>
    planItems.value
      .filter(item => item.dailyDietPlanId === planId)
      .sort((a, b) => a.sortOrder - b.sortOrder)
  )

  const getTodayPlan = computed(() => {
    const today = new Date().toISOString().split('T')[0]
    return getPlanByDate.value(today)
  })

  const getTodayPlanItems = computed(() => {
    const todayPlan = getTodayPlan.value
    return todayPlan ? getPlanItemsByPlanId.value(todayPlan.id) : []
  })

  const getConsumedItemsCount = computed(() => (planId: number) => {
    const items = getPlanItemsByPlanId.value(planId)
    return items.filter(item => item.isConsumed).length
  })

  const getTotalItemsCount = computed(() => (planId: number) => {
    return getPlanItemsByPlanId.value(planId).length
  })

  const getCompletionRate = computed(() => (planId: number) => {
    const total = getTotalItemsCount.value(planId)
    if (total === 0) return 0
    const consumed = getConsumedItemsCount.value(planId)
    return Math.round((consumed / total) * 100)
  })

  // 动作
  const fetchDailyPlans = async (userId: number, startDate?: string, endDate?: string) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 模拟数据
      const today = new Date().toISOString().split('T')[0]
      dailyPlans.value = [
        {
          id: 1,
          userId: 1,
          planDate: today,
          dietTypeId: 1, // 地中海饮食
          planName: '我的地中海饮食计划',
          notes: '今天开始尝试地中海饮食',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取饮食计划失败'
    } finally {
      loading.value = false
    }
  }

  const fetchPlanItems = async (planId: number) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      
      // 模拟数据 - 地中海饮食的计划项目
      const mockItems: DailyDietPlanItem[] = [
        {
          id: 1,
          dailyDietPlanId: planId,
          categoryName: '蔬菜',
          categoryDescription: '新鲜的各类蔬菜，富含维生素、矿物质和膳食纤维',
          specificFoods: [
            { id: '1', name: '橄榄油拌菠菜', description: '新鲜菠菜配橄榄油和柠檬汁', amount: '200', unit: 'g' },
            { id: '2', name: '烤茄子', description: '烤制的茄子片', amount: '150', unit: 'g' }
          ],
          isConsumed: false,
          sortOrder: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 2,
          dailyDietPlanId: planId,
          categoryName: '水果',
          categoryDescription: '新鲜的季节性水果，提供维生素C和抗氧化物质',
          specificFoods: [
            { id: '3', name: '新鲜橙子', amount: '1', unit: '个' },
            { id: '4', name: '葡萄', amount: '100', unit: 'g' }
          ],
          isConsumed: true,
          consumedAt: new Date().toISOString(),
          sortOrder: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 3,
          dailyDietPlanId: planId,
          categoryName: '全谷物',
          categoryDescription: '未精制的谷物，提供复合碳水化合物和B族维生素',
          specificFoods: [
            { id: '5', name: '全麦面包', amount: '2', unit: '片' }
          ],
          isConsumed: false,
          sortOrder: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 4,
          dailyDietPlanId: planId,
          categoryName: '豆类',
          categoryDescription: '各种豆类，富含植物蛋白和膳食纤维',
          isConsumed: false,
          sortOrder: 4,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 5,
          dailyDietPlanId: planId,
          categoryName: '坚果',
          categoryDescription: '各种坚果，提供健康脂肪和蛋白质',
          specificFoods: [
            { id: '6', name: '混合坚果', description: '核桃、杏仁、榛子混合', amount: '30', unit: 'g' }
          ],
          isConsumed: true,
          consumedAt: new Date().toISOString(),
          sortOrder: 5,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 6,
          dailyDietPlanId: planId,
          categoryName: '橄榄油',
          categoryDescription: '特级初榨橄榄油，主要的脂肪来源',
          isConsumed: false,
          sortOrder: 6,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 7,
          dailyDietPlanId: planId,
          categoryName: '鱼类',
          categoryDescription: '富含omega-3脂肪酸的鱼类',
          specificFoods: [
            { id: '7', name: '烤三文鱼', description: '香草烤三文鱼', amount: '150', unit: 'g' }
          ],
          isConsumed: false,
          sortOrder: 7,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]
      
      // 过滤出属于指定计划的项目
      const filteredItems = mockItems.filter(item => item.dailyDietPlanId === planId)
      
      // 更新或添加到现有项目中
      filteredItems.forEach(newItem => {
        const existingIndex = planItems.value.findIndex(item => item.id === newItem.id)
        if (existingIndex >= 0) {
          planItems.value[existingIndex] = newItem
        } else {
          planItems.value.push(newItem)
        }
      })
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取计划项目失败'
    } finally {
      loading.value = false
    }
  }

  // 创建每日饮食计划
  const createDailyPlan = async (plan: Omit<DailyDietPlan, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const newPlan: DailyDietPlan = {
        ...plan,
        id: Math.max(...dailyPlans.value.map(p => p.id), 0) + 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      dailyPlans.value.push(newPlan)
      return newPlan
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建饮食计划失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新每日饮食计划
  const updateDailyPlan = async (id: number, updates: Partial<DailyDietPlan>) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const index = dailyPlans.value.findIndex(p => p.id === id)
      if (index === -1) {
        throw new Error('饮食计划不存在')
      }
      
      dailyPlans.value[index] = {
        ...dailyPlans.value[index],
        ...updates,
        updatedAt: new Date().toISOString()
      }
      
      return dailyPlans.value[index]
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新饮食计划失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 标记项目为已摄入/未摄入
  const toggleItemConsumption = async (itemId: number, isConsumed: boolean) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const index = planItems.value.findIndex(item => item.id === itemId)
      if (index === -1) {
        throw new Error('计划项目不存在')
      }
      
      planItems.value[index] = {
        ...planItems.value[index],
        isConsumed,
        consumedAt: isConsumed ? new Date().toISOString() : undefined,
        updatedAt: new Date().toISOString()
      }
      
      return planItems.value[index]
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新摄入状态失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 添加具体食物到计划项目
  const addSpecificFoodToItem = async (itemId: number, food: SpecificFood) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const index = planItems.value.findIndex(item => item.id === itemId)
      if (index === -1) {
        throw new Error('计划项目不存在')
      }
      
      const currentFoods = planItems.value[index].specificFoods || []
      planItems.value[index] = {
        ...planItems.value[index],
        specificFoods: [...currentFoods, food],
        updatedAt: new Date().toISOString()
      }
      
      return planItems.value[index]
    } catch (err) {
      error.value = err instanceof Error ? err.message : '添加具体食物失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 删除具体食物
  const removeSpecificFoodFromItem = async (itemId: number, foodId: string) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const index = planItems.value.findIndex(item => item.id === itemId)
      if (index === -1) {
        throw new Error('计划项目不存在')
      }
      
      const currentFoods = planItems.value[index].specificFoods || []
      planItems.value[index] = {
        ...planItems.value[index],
        specificFoods: currentFoods.filter(food => food.id !== foodId),
        updatedAt: new Date().toISOString()
      }
      
      return planItems.value[index]
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除具体食物失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    dailyPlans,
    planItems,
    loading,
    error,
    
    // 计算属性
    getPlanByDate,
    getPlanItemsByPlanId,
    getTodayPlan,
    getTodayPlanItems,
    getConsumedItemsCount,
    getTotalItemsCount,
    getCompletionRate,
    
    // 动作
    fetchDailyPlans,
    fetchPlanItems,
    createDailyPlan,
    updateDailyPlan,
    toggleItemConsumption,
    addSpecificFoodToItem,
    removeSpecificFoodFromItem
  }
})
