import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface User {
  id: number
  username: string
  email: string
  nickname?: string
  avatar_url?: string
  gender?: 'male' | 'female' | 'other'
  birth_date?: string
  height?: number
  weight?: number
  activity_level?: 'sedentary' | 'light' | 'moderate' | 'active' | 'very_active'
}

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const isLoggedIn = ref(false)

  const setUser = (userData: User | null) => {
    user.value = userData
    isLoggedIn.value = !!userData
  }

  const logout = () => {
    user.value = null
    isLoggedIn.value = false
  }

  const updateProfile = (updates: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...updates }
    }
  }

  return {
    user,
    isLoggedIn,
    setUser,
    logout,
    updateProfile
  }
})
