import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { Food } from './food'

export interface NutritionRecord {
  id: number
  user_id: number
  record_date: string
  record_time: string // 新增：记录时间 (HH:mm 格式)
  food_id?: number // 改为可选，支持自定义食物
  food?: Food
  food_name?: string // 新增：自定义食物名称
  quantity: number
  unit: string

  // 实际摄入的营养成分
  actual_calories: number
  actual_carbohydrates: number
  actual_total_fat: number
  actual_protein: number
  actual_saturated_fat: number
  actual_trans_fat: number
  actual_cholesterol: number
  actual_sodium: number
  actual_potassium: number
  actual_dietary_fiber: number
  actual_sugar: number
  actual_vitamin_a: number
  actual_vitamin_c: number
  actual_calcium: number
  actual_iron: number

  // 扩展营养成分（支持自定义）
  actual_extended_nutrition?: Record<string, number>
  notes?: string
}

export interface DailySummary {
  id: number
  user_id: number
  summary_date: string
  
  total_calories: number
  total_carbohydrates: number
  total_fat: number
  total_protein: number
  total_saturated_fat: number
  total_trans_fat: number
  total_cholesterol: number
  total_sodium: number
  total_potassium: number
  total_dietary_fiber: number
  total_sugar: number
  total_vitamin_a: number
  total_vitamin_c: number
  total_calcium: number
  total_iron: number
  
  total_extended_nutrition?: Record<string, number>
}

export const useNutritionStore = defineStore('nutrition', () => {
  const records = ref<NutritionRecord[]>([])
  const dailySummaries = ref<DailySummary[]>([])
  const loading = ref(false)

  const setRecords = (recordList: NutritionRecord[]) => {
    records.value = recordList
  }

  const addRecord = (record: NutritionRecord) => {
    records.value.push(record)
  }

  const updateRecord = (id: number, updates: Partial<NutritionRecord>) => {
    const index = records.value.findIndex(r => r.id === id)
    if (index !== -1) {
      records.value[index] = { ...records.value[index], ...updates }
    }
  }

  const deleteRecord = (id: number) => {
    const index = records.value.findIndex(r => r.id === id)
    if (index !== -1) {
      records.value.splice(index, 1)
    }
  }

  const setDailySummaries = (summaries: DailySummary[]) => {
    dailySummaries.value = summaries
  }

  const getRecordsByDate = (date: string) => {
    return records.value.filter(r => r.record_date === date)
  }

  const getRecordsByDateRange = (date: string, startTime?: string, endTime?: string) => {
    const dateRecords = records.value.filter(r => r.record_date === date)
    if (!startTime || !endTime) return dateRecords

    return dateRecords.filter(r => {
      const recordTime = r.record_time
      return recordTime >= startTime && recordTime <= endTime
    })
  }

  const getDailySummary = (date: string) => {
    return dailySummaries.value.find(s => s.summary_date === date)
  }

  return {
    records,
    dailySummaries,
    loading,
    setRecords,
    addRecord,
    updateRecord,
    deleteRecord,
    setDailySummaries,
    getRecordsByDate,
    getRecordsByDateRange,
    getDailySummary
  }
})
