import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { ExtendedNutrition } from '../data/mockData'

export interface FoodTag {
  id: number
  name: string
  color: string
  description?: string
  is_system: boolean
  user_id?: number
}

export interface Food {
  id: number
  name: string
  brand?: string
  barcode?: string
  user_id: number
  is_public: boolean
  
  // 基础营养成分 (每100g)
  calories: number
  carbohydrates: number
  total_fat: number
  protein: number
  saturated_fat: number
  trans_fat: number
  cholesterol: number
  sodium: number
  potassium: number
  dietary_fiber: number
  sugar: number
  vitamin_a: number
  vitamin_c: number
  calcium: number
  iron: number

  // NRV% (营养素参考值百分比，每100g)
  calories_nrv: number
  carbohydrates_nrv: number
  total_fat_nrv: number
  protein_nrv: number
  saturated_fat_nrv: number
  cholesterol_nrv: number
  sodium_nrv: number
  potassium_nrv: number
  dietary_fiber_nrv: number
  vitamin_a_nrv: number
  vitamin_c_nrv: number
  calcium_nrv: number
  iron_nrv: number
  
  // 扩展营养成分
  extended_nutrition?: ExtendedNutrition
  
  serving_size: number
  serving_unit: string
  description?: string
  image_url?: string
  
  // 关联的标签
  tags?: FoodTag[]
}

export const useFoodStore = defineStore('food', () => {
  const foods = ref<Food[]>([])
  const tags = ref<FoodTag[]>([])
  const loading = ref(false)

  const setFoods = (foodList: Food[]) => {
    foods.value = foodList
  }

  const addFood = (food: Food) => {
    foods.value.push(food)
  }

  const updateFood = (id: number, updates: Partial<Food>) => {
    const index = foods.value.findIndex(f => f.id === id)
    if (index !== -1) {
      foods.value[index] = { ...foods.value[index], ...updates }
    }
  }

  const deleteFood = (id: number) => {
    const index = foods.value.findIndex(f => f.id === id)
    if (index !== -1) {
      foods.value.splice(index, 1)
    }
  }

  const setTags = (tagList: FoodTag[]) => {
    tags.value = tagList
  }

  const addTag = (tag: FoodTag) => {
    tags.value.push(tag)
  }

  const updateTag = (id: number, updates: Partial<FoodTag>) => {
    const index = tags.value.findIndex(t => t.id === id)
    if (index !== -1) {
      tags.value[index] = { ...tags.value[index], ...updates }
    }
  }

  const deleteTag = (id: number) => {
    const index = tags.value.findIndex(t => t.id === id)
    if (index !== -1) {
      tags.value.splice(index, 1)
    }
  }

  return {
    foods,
    tags,
    loading,
    setFoods,
    addFood,
    updateFood,
    deleteFood,
    setTags,
    addTag,
    updateTag,
    deleteTag
  }
})
