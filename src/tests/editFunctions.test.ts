// 编辑功能的简单测试
import { describe, it, expect, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useFoodStore } from '../stores/food'
import { useNutritionStore } from '../stores/nutrition'

describe('编辑功能测试', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  describe('食物编辑功能', () => {
    it('应该能够添加和更新食物', () => {
      const foodStore = useFoodStore()
      
      // 添加测试食物
      const testFood = {
        id: 1,
        name: '测试食物',
        brand: '测试品牌',
        barcode: '1234567890',
        user_id: 1,
        is_public: false,
        calories: 100,
        carbohydrates: 20,
        total_fat: 5,
        protein: 10,
        saturated_fat: 2,
        trans_fat: 0,
        cholesterol: 0,
        sodium: 100,
        potassium: 200,
        dietary_fiber: 3,
        sugar: 5,
        vitamin_a: 0,
        vitamin_c: 10,
        calcium: 50,
        iron: 2,
        calories_nrv: 5,
        carbohydrates_nrv: 7,
        total_fat_nrv: 8,
        protein_nrv: 17,
        saturated_fat_nrv: 10,
        cholesterol_nrv: 0,
        sodium_nrv: 5,
        potassium_nrv: 6,
        dietary_fiber_nrv: 12,
        vitamin_a_nrv: 0,
        vitamin_c_nrv: 10,
        calcium_nrv: 6,
        iron_nrv: 13,
        description: '这是一个测试食物'
      }

      foodStore.addFood(testFood)
      expect(foodStore.foods).toHaveLength(1)
      expect(foodStore.foods[0].name).toBe('测试食物')

      // 更新食物
      const updates = {
        name: '更新后的测试食物',
        calories: 150,
        protein: 15
      }

      foodStore.updateFood(1, updates)
      const updatedFood = foodStore.foods.find(f => f.id === 1)
      
      expect(updatedFood?.name).toBe('更新后的测试食物')
      expect(updatedFood?.calories).toBe(150)
      expect(updatedFood?.protein).toBe(15)
      expect(updatedFood?.brand).toBe('测试品牌') // 其他字段应该保持不变
    })

    it('应该能够删除食物', () => {
      const foodStore = useFoodStore()
      
      const testFood = {
        id: 2,
        name: '待删除食物',
        brand: '',
        barcode: '',
        user_id: 1,
        is_public: false,
        calories: 100,
        carbohydrates: 20,
        total_fat: 5,
        protein: 10,
        saturated_fat: 2,
        trans_fat: 0,
        cholesterol: 0,
        sodium: 100,
        potassium: 200,
        dietary_fiber: 3,
        sugar: 5,
        vitamin_a: 0,
        vitamin_c: 10,
        calcium: 50,
        iron: 2,
        calories_nrv: 5,
        carbohydrates_nrv: 7,
        total_fat_nrv: 8,
        protein_nrv: 17,
        saturated_fat_nrv: 10,
        cholesterol_nrv: 0,
        sodium_nrv: 5,
        potassium_nrv: 6,
        dietary_fiber_nrv: 12,
        vitamin_a_nrv: 0,
        vitamin_c_nrv: 10,
        calcium_nrv: 6,
        iron_nrv: 13
      }

      foodStore.addFood(testFood)
      expect(foodStore.foods).toHaveLength(1)

      foodStore.deleteFood(2)
      expect(foodStore.foods).toHaveLength(0)
    })
  })

  describe('营养记录编辑功能', () => {
    it('应该能够添加和更新营养记录', () => {
      const nutritionStore = useNutritionStore()
      
      // 添加测试记录
      const testRecord = {
        id: 1,
        user_id: 1,
        record_date: '2024-01-15',
        record_time: '12:00',
        food_id: 1,
        quantity: 100,
        unit: '克',
        actual_calories: 100,
        actual_carbohydrates: 20,
        actual_total_fat: 5,
        actual_protein: 10,
        actual_saturated_fat: 2,
        actual_trans_fat: 0,
        actual_cholesterol: 0,
        actual_sodium: 100,
        actual_potassium: 200,
        actual_dietary_fiber: 3,
        actual_sugar: 5,
        actual_vitamin_a: 0,
        actual_vitamin_c: 10,
        actual_calcium: 50,
        actual_iron: 2,
        notes: '测试记录'
      }

      nutritionStore.addRecord(testRecord)
      expect(nutritionStore.records).toHaveLength(1)
      expect(nutritionStore.records[0].notes).toBe('测试记录')

      // 更新记录
      const updates = {
        quantity: 150,
        actual_calories: 150,
        actual_protein: 15,
        notes: '更新后的测试记录'
      }

      nutritionStore.updateRecord(1, updates)
      const updatedRecord = nutritionStore.records.find(r => r.id === 1)
      
      expect(updatedRecord?.quantity).toBe(150)
      expect(updatedRecord?.actual_calories).toBe(150)
      expect(updatedRecord?.actual_protein).toBe(15)
      expect(updatedRecord?.notes).toBe('更新后的测试记录')
      expect(updatedRecord?.record_date).toBe('2024-01-15') // 其他字段应该保持不变
    })

    it('应该能够删除营养记录', () => {
      const nutritionStore = useNutritionStore()
      
      const testRecord = {
        id: 2,
        user_id: 1,
        record_date: '2024-01-16',
        record_time: '13:00',
        food_id: 1,
        quantity: 100,
        unit: '克',
        actual_calories: 100,
        actual_carbohydrates: 20,
        actual_total_fat: 5,
        actual_protein: 10,
        actual_saturated_fat: 2,
        actual_trans_fat: 0,
        actual_cholesterol: 0,
        actual_sodium: 100,
        actual_potassium: 200,
        actual_dietary_fiber: 3,
        actual_sugar: 5,
        actual_vitamin_a: 0,
        actual_vitamin_c: 10,
        actual_calcium: 50,
        actual_iron: 2,
        notes: '待删除记录'
      }

      nutritionStore.addRecord(testRecord)
      expect(nutritionStore.records).toHaveLength(1)

      nutritionStore.deleteRecord(2)
      expect(nutritionStore.records).toHaveLength(0)
    })

    it('应该能够按日期获取记录', () => {
      const nutritionStore = useNutritionStore()
      
      const record1 = {
        id: 3,
        user_id: 1,
        record_date: '2024-01-15',
        record_time: '12:00',
        food_id: 1,
        quantity: 100,
        unit: '克',
        actual_calories: 100,
        actual_carbohydrates: 20,
        actual_total_fat: 5,
        actual_protein: 10,
        actual_saturated_fat: 2,
        actual_trans_fat: 0,
        actual_cholesterol: 0,
        actual_sodium: 100,
        actual_potassium: 200,
        actual_dietary_fiber: 3,
        actual_sugar: 5,
        actual_vitamin_a: 0,
        actual_vitamin_c: 10,
        actual_calcium: 50,
        actual_iron: 2,
        notes: '记录1'
      }

      const record2 = {
        id: 4,
        user_id: 1,
        record_date: '2024-01-16',
        record_time: '13:00',
        food_id: 1,
        quantity: 150,
        unit: '克',
        actual_calories: 150,
        actual_carbohydrates: 30,
        actual_total_fat: 7.5,
        actual_protein: 15,
        actual_saturated_fat: 3,
        actual_trans_fat: 0,
        actual_cholesterol: 0,
        actual_sodium: 150,
        actual_potassium: 300,
        actual_dietary_fiber: 4.5,
        actual_sugar: 7.5,
        actual_vitamin_a: 0,
        actual_vitamin_c: 15,
        actual_calcium: 75,
        actual_iron: 3,
        notes: '记录2'
      }

      nutritionStore.addRecord(record1)
      nutritionStore.addRecord(record2)

      const records2024_01_15 = nutritionStore.getRecordsByDate('2024-01-15')
      const records2024_01_16 = nutritionStore.getRecordsByDate('2024-01-16')

      expect(records2024_01_15).toHaveLength(1)
      expect(records2024_01_15[0].notes).toBe('记录1')
      
      expect(records2024_01_16).toHaveLength(1)
      expect(records2024_01_16[0].notes).toBe('记录2')
    })
  })
})
