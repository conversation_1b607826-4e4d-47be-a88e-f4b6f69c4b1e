import { ref, onMounted, onUnmounted } from 'vue'

// 响应式断点定义
export const BREAKPOINTS = {
  xs: 480,   // 手机竖屏
  sm: 768,   // 手机横屏/小平板
  md: 1024,  // 平板
  lg: 1280,  // 小桌面
  xl: 1536,  // 大桌面
} as const

export type Breakpoint = keyof typeof BREAKPOINTS

// 设备检测工具
export function detectDevice() {
  const userAgent = navigator.userAgent.toLowerCase()
  const platform = navigator.platform?.toLowerCase() || ''

  // 检测移动设备
  const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)

  // 检测平板设备
  const isTabletDevice = /ipad|android(?!.*mobile)|tablet/i.test(userAgent) ||
    (platform.includes('mac') && 'ontouchend' in document)

  // 检测桌面设备
  const isDesktopDevice = !isMobileDevice && !isTabletDevice

  // 检测触摸设备
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0

  // 检测 iOS 设备
  const isIOS = /iphone|ipad|ipod/i.test(userAgent)

  // 检测 Android 设备
  const isAndroid = /android/i.test(userAgent)

  return {
    isMobileDevice,
    isTabletDevice,
    isDesktopDevice,
    isTouchDevice,
    isIOS,
    isAndroid,
    userAgent,
    platform
  }
}

export function useResponsive() {
  const windowWidth = ref(0)
  const windowHeight = ref(0)

  // 设备检测结果
  const deviceInfo = detectDevice()

  // 更新窗口尺寸
  const updateSize = () => {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
  }

  // 基于设备类型和屏幕尺寸的智能判断
  const isMobile = ref(false)
  const isTablet = ref(false)
  const isDesktop = ref(false)

  // 检查具体断点
  const isXs = ref(false)  // <= 480px
  const isSm = ref(false)  // <= 768px
  const isMd = ref(false)  // <= 1024px
  const isLg = ref(false)  // <= 1280px
  const isXl = ref(false)  // > 1280px

  // 更新断点状态
  const updateBreakpoints = () => {
    const width = windowWidth.value

    isXs.value = width <= BREAKPOINTS.xs
    isSm.value = width <= BREAKPOINTS.sm
    isMd.value = width <= BREAKPOINTS.md
    isLg.value = width <= BREAKPOINTS.lg
    isXl.value = width > BREAKPOINTS.lg

    // 智能设备类型判断：结合设备检测和屏幕尺寸
    if (deviceInfo.isMobileDevice) {
      // 如果是移动设备，即使屏幕较大也优先使用移动端布局
      isMobile.value = true
      isTablet.value = false
      isDesktop.value = false
    } else if (deviceInfo.isTabletDevice || (width > BREAKPOINTS.sm && width <= BREAKPOINTS.md)) {
      // 平板设备或中等屏幕尺寸
      isMobile.value = false
      isTablet.value = true
      isDesktop.value = false
    } else {
      // 桌面设备
      isMobile.value = false
      isTablet.value = false
      isDesktop.value = true
    }
  }

  // 获取当前断点
  const getCurrentBreakpoint = (): Breakpoint => {
    const width = windowWidth.value
    if (width <= BREAKPOINTS.xs) return 'xs'
    if (width <= BREAKPOINTS.sm) return 'sm'
    if (width <= BREAKPOINTS.md) return 'md'
    if (width <= BREAKPOINTS.lg) return 'lg'
    return 'xl'
  }

  // 检查是否匹配指定断点
  const matches = (breakpoint: Breakpoint): boolean => {
    return windowWidth.value <= BREAKPOINTS[breakpoint]
  }

  // 检查是否在断点范围内
  const between = (min: Breakpoint, max: Breakpoint): boolean => {
    const width = windowWidth.value
    return width > BREAKPOINTS[min] && width <= BREAKPOINTS[max]
  }

  // 获取响应式网格列数
  const getGridCols = (config: {
    xs?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
    default?: number
  }): number => {
    const breakpoint = getCurrentBreakpoint()
    return config[breakpoint] ?? config.default ?? 1
  }

  // 获取响应式间距
  const getSpacing = (config: {
    xs?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
    default?: number
  }): number => {
    const breakpoint = getCurrentBreakpoint()
    return config[breakpoint] ?? config.default ?? 16
  }

  // 处理窗口大小变化
  const handleResize = () => {
    updateSize()
    updateBreakpoints()
  }

  onMounted(() => {
    updateSize()
    updateBreakpoints()
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })

  return {
    // 窗口尺寸
    windowWidth,
    windowHeight,

    // 设备类型
    isMobile,
    isTablet,
    isDesktop,

    // 设备信息
    deviceInfo,

    // 断点检查
    isXs,
    isSm,
    isMd,
    isLg,
    isXl,

    // 工具函数
    getCurrentBreakpoint,
    matches,
    between,
    getGridCols,
    getSpacing,
  }
}

// 预设的响应式配置
export const RESPONSIVE_CONFIGS = {
  // 网格列数配置
  gridCols: {
    // 营养概览卡片
    nutritionCards: {
      xs: 2,
      sm: 2,
      md: 4,
      lg: 4,
      xl: 4,
    },
    // 表单字段
    formFields: {
      xs: 1,
      sm: 1,
      md: 2,
      lg: 2,
      xl: 2,
    },
    // 食物卡片
    foodCards: {
      xs: 1,
      sm: 1,
      md: 2,
      lg: 3,
      xl: 4,
    },
  },
  
  // 间距配置
  spacing: {
    // 卡片间距
    cardGap: {
      xs: 8,
      sm: 12,
      md: 16,
      lg: 20,
      xl: 24,
    },
    // 页面内边距
    pagePadding: {
      xs: 12,
      sm: 16,
      md: 24,
      lg: 32,
      xl: 40,
    },
  },
} as const
