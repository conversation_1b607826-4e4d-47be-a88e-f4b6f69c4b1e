import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../views/Dashboard.vue'),
    meta: { title: '营养仪表盘' }
  },
  {
    path: '/foods',
    name: 'Foods',
    component: () => import('../views/Foods.vue'),
    meta: { title: '食物库' }
  },
  {
    path: '/foods/add',
    name: 'AddFood',
    component: () => import('../views/AddFood.vue'),
    meta: { title: '添加食物' }
  },
  {
    path: '/foods/:id/edit',
    name: 'EditFood',
    component: () => import('../views/EditFood.vue'),
    meta: { title: '编辑食物' }
  },
  {
    path: '/records',
    name: 'Records',
    component: () => import('../views/Records.vue'),
    meta: { title: '营养记录' }
  },
  {
    path: '/records/add',
    name: 'AddRecord',
    component: () => import('../views/AddRecord.vue'),
    meta: { title: '添加记录' }
  },
  {
    path: '/records/:id/edit',
    name: 'EditRecord',
    component: () => import('../views/EditRecord.vue'),
    meta: { title: '编辑记录' }
  },
  {
    path: '/tags',
    name: 'Tags',
    component: () => import('../views/Tags.vue'),
    meta: { title: '标签管理' }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../views/Profile.vue'),
    meta: { title: '个人资料' }
  },
  {
    path: '/diet-types',
    name: 'DietTypes',
    component: () => import('../views/DietTypes.vue'),
    meta: { title: '饮食类型管理' }
  },
  {
    path: '/diet-plan',
    name: 'DailyDietPlan',
    component: () => import('../views/DailyDietPlan.vue'),
    meta: { title: '每日饮食计划' }
  },
  {
    path: '/diet-tracker',
    name: 'DailyDietTracker',
    component: () => import('../views/DailyDietTracker.vue'),
    meta: { title: '每日饮食跟踪' }
  }
]

const router = createRouter({
  history: createWebHistory('/nutrition'),
  routes
})

router.beforeEach((to, _from, next) => {
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 营养追踪器`
  }
  next()
})

export default router
