<template>
  <div class="diet-type-detail">
    <div class="detail-header">
      <div class="title-section">
        <div 
          class="color-indicator" 
          :style="{ backgroundColor: dietType.color }"
        ></div>
        <div>
          <h2>{{ dietType.name }}</h2>
          <p class="english-name" v-if="dietType.nameEn">{{ dietType.nameEn }}</p>
        </div>
      </div>
      <div class="badges">
        <NTag v-if="dietType.isSystem" type="info" size="small">系统预设</NTag>
      </div>
    </div>

    <div class="detail-content">
      <div class="section">
        <h3>饮食描述</h3>
        <p>{{ dietType.description }}</p>
      </div>

      <div class="section">
        <h3>营养特点</h3>
        <p>{{ dietType.nutritionFeatures }}</p>
      </div>

      <div class="section">
        <h3>主要益处</h3>
        <p>{{ dietType.benefits }}</p>
      </div>

      <div class="section">
        <h3>适合人群</h3>
        <p>{{ dietType.suitablePeople }}</p>
      </div>

      <div class="section" v-if="foodCategories.length > 0">
        <h3>包含的食物分类</h3>
        <div class="food-categories">
          <NCard
            v-for="category in foodCategories"
            :key="category.id"
            class="category-card"
            size="small"
          >
            <template #header>
              <div class="category-header">
                <h4>{{ category.categoryName }}</h4>
                <span class="category-name-en" v-if="category.categoryNameEn">
                  {{ category.categoryNameEn }}
                </span>
              </div>
            </template>

            <div class="category-content">
              <p class="category-description">{{ category.description }}</p>
              
              <div class="category-info">
                <div class="info-item">
                  <span class="label">食物示例：</span>
                  <span class="value">{{ category.examples }}</span>
                </div>
                <div class="info-item">
                  <span class="label">推荐摄入量：</span>
                  <span class="value">{{ category.recommendedAmount }}</span>
                </div>
              </div>
            </div>
          </NCard>
        </div>
      </div>

      <div class="section" v-if="dietType.imageUrl">
        <h3>相关图片</h3>
        <div class="image-container">
          <img 
            :src="dietType.imageUrl" 
            :alt="dietType.name"
            class="diet-image"
            @error="handleImageError"
          />
        </div>
      </div>

      <div class="section meta-info">
        <h3>其他信息</h3>
        <div class="meta-grid">
          <div class="meta-item">
            <span class="meta-label">创建时间：</span>
            <span class="meta-value">{{ formatDate(dietType.createdAt) }}</span>
          </div>
          <div class="meta-item">
            <span class="meta-label">更新时间：</span>
            <span class="meta-value">{{ formatDate(dietType.updatedAt) }}</span>
          </div>
          <div class="meta-item">
            <span class="meta-label">主题颜色：</span>
            <span class="meta-value">
              <span 
                class="color-sample" 
                :style="{ backgroundColor: dietType.color }"
              ></span>
              {{ dietType.color }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NCard, NTag } from 'naive-ui'
import type { DietType, DietTypeFoodCategory } from '../stores/dietType'

interface Props {
  dietType: DietType
  foodCategories: DietTypeFoodCategory[]
}

defineProps<Props>()

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}
</script>

<style scoped>
.diet-type-detail {
  width: 100%;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-indicator {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  flex-shrink: 0;
}

.title-section h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.english-name {
  margin: 4px 0 0 0;
  font-size: 16px;
  color: #666;
  font-style: italic;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section p {
  margin: 0;
  line-height: 1.6;
  color: #555;
}

.food-categories {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.category-card {
  height: fit-content;
}

.category-header {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.category-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.category-name-en {
  font-size: 14px;
  color: #666;
  font-style: italic;
}

.category-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.category-description {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #555;
}

.category-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item .label {
  font-size: 12px;
  font-weight: 600;
  color: #666;
}

.info-item .value {
  font-size: 14px;
  color: #333;
}

.image-container {
  display: flex;
  justify-content: center;
  margin-top: 12px;
}

.diet-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.meta-info {
  background-color: #fafafa;
  padding: 16px;
  border-radius: 8px;
  border: none;
}

.meta-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.meta-label {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.meta-value {
  font-size: 14px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 6px;
}

.color-sample {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid #e0e0e0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .food-categories {
    grid-template-columns: 1fr;
  }
  
  .meta-grid {
    grid-template-columns: 1fr;
  }
  
  .info-item {
    flex-direction: row;
    align-items: flex-start;
  }
  
  .info-item .label {
    min-width: 80px;
    flex-shrink: 0;
  }
}
</style>
