<template>
  <div class="create-diet-plan-form">
    <NForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <NFormItem label="计划名称" path="planName">
        <NInput
          v-model:value="formData.planName"
          placeholder="请输入计划名称，如'我的地中海饮食计划'"
          maxlength="200"
          show-count
        />
      </NFormItem>

      <NFormItem label="选择饮食类型" path="dietTypeId">
        <NSelect
          v-model:value="formData.dietTypeId"
          placeholder="请选择饮食类型（可选）"
          :options="dietTypeOptions"
          clearable
          @update:value="handleDietTypeChange"
        />
      </NFormItem>

      <!-- 饮食类型预览 -->
      <div v-if="selectedDietType" class="diet-type-preview">
        <NCard size="small" class="preview-card">
          <template #header>
            <div class="preview-header">
              <div 
                class="color-indicator" 
                :style="{ backgroundColor: selectedDietType.color }"
              ></div>
              <span>{{ selectedDietType.name }}</span>
            </div>
          </template>
          <div class="preview-content">
            <p class="preview-description">{{ selectedDietType.description }}</p>
            <div class="preview-features">
              <h4>营养特点</h4>
              <p>{{ selectedDietType.nutritionFeatures }}</p>
            </div>
            <div class="preview-categories" v-if="dietTypeCategories.length > 0">
              <h4>包含的食物分类 ({{ dietTypeCategories.length }}类)</h4>
              <div class="categories-tags">
                <NTag
                  v-for="category in dietTypeCategories"
                  :key="category.id"
                  size="small"
                  type="info"
                >
                  {{ category.categoryName }}
                </NTag>
              </div>
            </div>
          </div>
        </NCard>
      </div>

      <NFormItem label="计划备注" path="notes">
        <NInput
          v-model:value="formData.notes"
          type="textarea"
          placeholder="请输入计划备注（可选）"
          :rows="3"
          maxlength="500"
          show-count
        />
      </NFormItem>

      <NFormItem label="自动创建食物分类">
        <NCheckbox
          v-model:checked="formData.autoCreateCategories"
          :disabled="!formData.dietTypeId"
        >
          根据选择的饮食类型自动创建食物分类
        </NCheckbox>
        <div class="checkbox-help">
          <NText depth="3" style="font-size: 12px;">
            勾选后将自动为您创建该饮食类型包含的所有食物分类
          </NText>
        </div>
      </NFormItem>
    </NForm>

    <div class="form-actions">
      <NSpace>
        <NButton @click="handleCancel">取消</NButton>
        <NButton
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ props.initialData ? '更新计划' : '创建计划' }}
        </NButton>
      </NSpace>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import {
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NCheckbox,
  NButton,
  NSpace,
  NCard,
  NTag,
  NText,
  type FormInst,
  type FormRules,
  type SelectOption
} from 'naive-ui'
import { useDietTypeStore } from '../stores/dietType'

interface Props {
  selectedDate: string
  initialData?: {
    planName?: string
    dietTypeId?: number
    notes?: string
  }
}

interface Emits {
  (e: 'save', data: {
    planName: string
    dietTypeId?: number
    notes?: string
    autoCreateCategories: boolean
  }): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dietTypeStore = useDietTypeStore()
const formRef = ref<FormInst | null>(null)
const loading = ref(false)

// 表单数据
const formData = reactive({
  planName: '',
  dietTypeId: undefined as number | undefined,
  notes: '',
  autoCreateCategories: true
})

// 计算属性
const dietTypeOptions = computed<SelectOption[]>(() => {
  return dietTypeStore.dietTypes.map(type => ({
    label: type.name,
    value: type.id,
    disabled: false
  }))
})

const selectedDietType = computed(() => {
  if (!formData.dietTypeId) return null
  return dietTypeStore.getDietTypeById(formData.dietTypeId)
})

const dietTypeCategories = computed(() => {
  if (!formData.dietTypeId) return []
  return dietTypeStore.getFoodCategoriesByDietType(formData.dietTypeId)
})

// 表单验证规则
const rules: FormRules = {
  planName: [
    {
      required: true,
      message: '请输入计划名称',
      trigger: ['input', 'blur']
    },
    {
      min: 2,
      max: 200,
      message: '计划名称长度应在2-200个字符之间',
      trigger: ['input', 'blur']
    }
  ],
  notes: [
    {
      max: 500,
      message: '备注长度不能超过500个字符',
      trigger: ['input', 'blur']
    }
  ]
}

// 方法
const handleDietTypeChange = (value: number | null) => {
  if (value) {
    formData.autoCreateCategories = true
  } else {
    formData.autoCreateCategories = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true
    
    const submitData = {
      planName: formData.planName,
      dietTypeId: formData.dietTypeId,
      notes: formData.notes || undefined,
      autoCreateCategories: formData.autoCreateCategories
    }
    
    emit('save', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 生命周期
onMounted(async () => {
  // 确保饮食类型数据已加载
  if (dietTypeStore.dietTypes.length === 0) {
    await dietTypeStore.fetchDietTypes()
  }
  if (dietTypeStore.foodCategories.length === 0) {
    await dietTypeStore.fetchFoodCategories()
  }

  // 如果有初始数据，使用初始数据填充表单
  if (props.initialData) {
    formData.planName = props.initialData.planName || ''
    formData.dietTypeId = props.initialData.dietTypeId
    formData.notes = props.initialData.notes || ''
    formData.autoCreateCategories = false // 编辑模式下默认不自动创建分类
  } else {
    // 设置默认计划名称
    const date = new Date(props.selectedDate)
    const dateStr = date.toLocaleDateString('zh-CN', {
      month: 'long',
      day: 'numeric'
    })
    formData.planName = `${dateStr}的饮食计划`
  }
})
</script>

<style scoped>
.create-diet-plan-form {
  width: 100%;
  max-width: 600px;
}

.diet-type-preview {
  margin: 16px 0;
}

.preview-card {
  border: 1px solid #e0e0e0;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.preview-description {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.preview-features h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.preview-features p {
  margin: 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.preview-categories h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.categories-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.checkbox-help {
  margin-top: 4px;
}

.form-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

:deep(.n-form-item-label) {
  font-weight: 500;
}

:deep(.n-input) {
  border-radius: 6px;
}

:deep(.n-select) {
  border-radius: 6px;
}
</style>
