<template>
  <div class="diet-plan-calendar">
    <div class="calendar-header">
      <h3>饮食计划日历</h3>
      <div class="legend">
        <div class="legend-item">
          <div class="legend-dot completed"></div>
          <span>已完成</span>
        </div>
        <div class="legend-item">
          <div class="legend-dot partial"></div>
          <span>部分完成</span>
        </div>
        <div class="legend-item">
          <div class="legend-dot planned"></div>
          <span>已计划</span>
        </div>
        <div class="legend-item">
          <div class="legend-dot empty"></div>
          <span>无计划</span>
        </div>
      </div>
    </div>

    <VCalendar
      v-model="selectedDate"
      :attributes="calendarAttributes"
      :locale="locale"
      :first-day-of-week="2"
      expanded
      borderless
      @dayclick="handleDayClick"
    />

    <!-- 日期详情模态框 -->
    <NModal
      v-model:show="showDetailModal"
      preset="card"
      :title="modalTitle"
      style="width: 600px; max-width: 90vw;"
    >
      <div v-if="selectedDayPlan" class="day-detail">
        <div class="plan-info">
          <h4>{{ selectedDayPlan.planName || '饮食计划' }}</h4>
          <div class="plan-meta">
            <NTag :color="getDietTypeColor(selectedDayPlan.dietTypeId)" size="small">
              {{ getDietTypeName(selectedDayPlan.dietTypeId) }}
            </NTag>
            <span class="completion-rate">
              完成度: {{ getCompletionRate(selectedDayPlan.id) }}%
            </span>
          </div>
        </div>

        <div class="plan-items">
          <h5>食物分类</h5>
          <div class="items-list">
            <div
              v-for="item in getSelectedDayItems()"
              :key="item.id"
              class="item-row"
              :class="{ completed: item.isConsumed }"
            >
              <NCheckbox
                :checked="item.isConsumed"
                @update:checked="(checked) => toggleItemConsumption(item.id, checked)"
              />
              <div class="item-content">
                <span class="item-name">{{ item.categoryName }}</span>
                <span v-if="item.consumedAt" class="consumed-time">
                  {{ formatTime(item.consumedAt) }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div v-if="selectedDayPlan.notes" class="plan-notes">
          <h5>备注</h5>
          <p>{{ selectedDayPlan.notes }}</p>
        </div>
      </div>

      <div v-else class="no-plan">
        <NEmpty description="当天没有饮食计划">
          <template #extra>
            <NButton type="primary" @click="createPlanForDate">
              创建计划
            </NButton>
          </template>
        </NEmpty>
      </div>
    </NModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { Calendar as VCalendar } from 'v-calendar'
import {
  NModal,
  NTag,
  NCheckbox,
  NButton,
  NEmpty,
  useMessage
} from 'naive-ui'
import { useDailyDietPlanStore } from '../stores/dailyDietPlan'
import { useDietTypeStore } from '../stores/dietType'
import { useRouter } from 'vue-router'
import 'v-calendar/style.css'

const dailyPlanStore = useDailyDietPlanStore()
const dietTypeStore = useDietTypeStore()
const router = useRouter()
const message = useMessage()

// 响应式状态
const selectedDate = ref(new Date())
const showDetailModal = ref(false)
const selectedDayPlan = ref<any>(null)

// 本地化配置
const locale = ref({
  id: 'zh-CN',
  firstDayOfWeek: 2, // 周一开始
  masks: {
    weekdays: 'WWW',
    navMonths: 'MMMM',
    title: 'MMMM YYYY',
    dayPopover: 'WWW, MMM D, YYYY'
  }
})

// 计算属性
const modalTitle = computed(() => {
  if (!selectedDate.value) return ''
  return `${selectedDate.value.getFullYear()}年${selectedDate.value.getMonth() + 1}月${selectedDate.value.getDate()}日`
})

// 日历属性 - 根据饮食计划数据生成
const calendarAttributes = computed(() => {
  const attributes: any[] = []
  
  dailyPlanStore.dailyPlans.forEach(plan => {
    const planDate = new Date(plan.planDate)
    const completionRate = getCompletionRate(plan.id)
    
    let dotColor = '#d9d9d9' // 默认灰色（无计划）
    let barColor = '#d9d9d9'
    
    if (completionRate === 100) {
      dotColor = '#52c41a' // 绿色（已完成）
      barColor = '#52c41a'
    } else if (completionRate > 0) {
      dotColor = '#faad14' // 橙色（部分完成）
      barColor = '#faad14'
    } else {
      dotColor = '#1890ff' // 蓝色（已计划但未开始）
      barColor = '#1890ff'
    }

    attributes.push({
      key: `plan-${plan.id}`,
      dates: planDate,
      dot: {
        color: dotColor,
        class: 'diet-plan-dot'
      },
      bar: {
        color: barColor,
        class: 'diet-plan-bar'
      },
      popover: {
        label: `${plan.planName || '饮食计划'} (${completionRate}%)`
      }
    })
  })
  
  return attributes
})

// 方法
const handleDayClick = (day: any) => {
  selectedDate.value = day.date
  const dateString = day.date.toISOString().split('T')[0]
  selectedDayPlan.value = dailyPlanStore.getPlanByDate(dateString)
  showDetailModal.value = true
}

const getCompletionRate = (planId: number): number => {
  const items = dailyPlanStore.getPlanItemsByPlanId(planId)
  if (items.length === 0) return 0
  
  const completedItems = items.filter(item => item.isConsumed).length
  return Math.round((completedItems / items.length) * 100)
}

const getDietTypeName = (dietTypeId?: number): string => {
  if (!dietTypeId) return '自定义计划'
  const dietType = dietTypeStore.dietTypes.find(dt => dt.id === dietTypeId)
  return dietType?.name || '未知类型'
}

const getDietTypeColor = (dietTypeId?: number): string => {
  if (!dietTypeId) return '#d9d9d9'
  const dietType = dietTypeStore.dietTypes.find(dt => dt.id === dietTypeId)
  return dietType?.color || '#1890ff'
}

const getSelectedDayItems = () => {
  if (!selectedDayPlan.value) return []
  return dailyPlanStore.getPlanItemsByPlanId(selectedDayPlan.value.id)
}

const toggleItemConsumption = async (itemId: number, consumed: boolean) => {
  try {
    await dailyPlanStore.toggleItemConsumption(itemId, consumed)
    message.success(consumed ? '已标记为摄入' : '已取消摄入标记')
  } catch (error) {
    message.error('操作失败')
  }
}

const formatTime = (timeString: string): string => {
  return new Date(timeString).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const createPlanForDate = () => {
  const dateString = selectedDate.value.toISOString().split('T')[0]
  router.push({
    name: 'DailyDietPlan',
    query: { date: dateString }
  })
  showDetailModal.value = false
}

// 生命周期
onMounted(async () => {
  await dietTypeStore.fetchDietTypes()
  await dailyPlanStore.fetchDailyPlans(1) // 假设用户ID为1
  
  // 获取所有计划的详细项目
  for (const plan of dailyPlanStore.dailyPlans) {
    await dailyPlanStore.fetchPlanItems(plan.id)
  }
})
</script>

<style scoped>
.diet-plan-calendar {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.calendar-header h3 {
  margin: 0;
  color: #262626;
}

.legend {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-dot.completed {
  background-color: #52c41a;
}

.legend-dot.partial {
  background-color: #faad14;
}

.legend-dot.planned {
  background-color: #1890ff;
}

.legend-dot.empty {
  background-color: #d9d9d9;
}

.day-detail {
  padding: 0;
}

.plan-info {
  margin-bottom: 20px;
}

.plan-info h4 {
  margin: 0 0 8px 0;
  color: #262626;
}

.plan-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.completion-rate {
  font-size: 14px;
  color: #666;
}

.plan-items h5,
.plan-notes h5 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.item-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.item-row:hover {
  background-color: #f5f5f5;
}

.item-row.completed {
  background-color: #f6ffed;
}

.item-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-name {
  font-size: 14px;
  color: #262626;
}

.consumed-time {
  font-size: 12px;
  color: #999;
}

.plan-notes {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.plan-notes p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.no-plan {
  text-align: center;
  padding: 40px 20px;
}

/* V-Calendar 自定义样式 */
:deep(.vc-container) {
  border: none;
  border-radius: 8px;
}

:deep(.vc-header) {
  padding: 16px 20px;
}

:deep(.vc-weeks) {
  padding: 0 20px 20px;
}

:deep(.vc-day) {
  min-height: 40px;
}

:deep(.diet-plan-dot) {
  width: 6px;
  height: 6px;
}

:deep(.diet-plan-bar) {
  height: 3px;
  border-radius: 2px;
}
</style>
