<template>
  <div class="add-category-form">
    <div class="form-header">
      <h4>添加自定义食物分类</h4>
      <p class="form-description">为您的饮食计划添加自定义的食物分类</p>
    </div>

    <NForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <NFormItem label="分类名称" path="categoryName">
        <NInput
          v-model:value="formData.categoryName"
          placeholder="请输入分类名称，如'汤品'、'饮品'等"
          maxlength="100"
          show-count
        />
      </NFormItem>

      <NFormItem label="分类描述" path="categoryDescription">
        <NInput
          v-model:value="formData.categoryDescription"
          type="textarea"
          placeholder="请输入分类描述（可选）"
          :rows="2"
          maxlength="300"
          show-count
        />
      </NFormItem>

      <!-- 快速选择常见分类 -->
      <div class="quick-categories">
        <NFormItem label="快速选择">
          <div class="categories-container">
            <p class="categories-label">常见分类：</p>
            <div class="categories-list">
              <NButton
                v-for="category in commonCategories"
                :key="category.name"
                size="small"
                quaternary
                type="info"
                @click="applyCategory(category)"
              >
                {{ category.name }}
              </NButton>
            </div>
          </div>
        </NFormItem>
      </div>
    </NForm>

    <div class="form-actions">
      <NSpace>
        <NButton @click="handleCancel">取消</NButton>
        <NButton
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          添加分类
        </NButton>
      </NSpace>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
  NForm,
  NFormItem,
  NInput,
  NButton,
  NSpace,
  type FormInst,
  type FormRules
} from 'naive-ui'

interface Emits {
  (e: 'save', data: {
    categoryName: string
    categoryDescription?: string
  }): void
  (e: 'cancel'): void
}

interface CategorySuggestion {
  name: string
  description: string
}

const emit = defineEmits<Emits>()

const formRef = ref<FormInst | null>(null)
const loading = ref(false)

// 表单数据
const formData = reactive({
  categoryName: '',
  categoryDescription: ''
})

// 常见分类建议
const commonCategories: CategorySuggestion[] = [
  { name: '汤品', description: '各种汤类，如蔬菜汤、骨头汤等' },
  { name: '饮品', description: '各种饮料，如茶、咖啡、果汁等' },
  { name: '调料', description: '各种调味料和香料' },
  { name: '零食', description: '健康的零食和小食' },
  { name: '甜品', description: '健康的甜品和点心' },
  { name: '发酵食品', description: '酸奶、泡菜、纳豆等发酵食品' },
  { name: '种子类', description: '各种种子，如亚麻籽、奇亚籽等' },
  { name: '香草香料', description: '新鲜或干燥的香草和香料' },
  { name: '油脂类', description: '各种健康油脂' },
  { name: '蛋类', description: '鸡蛋及其制品' }
]

// 表单验证规则
const rules: FormRules = {
  categoryName: [
    {
      required: true,
      message: '请输入分类名称',
      trigger: ['input', 'blur']
    },
    {
      min: 1,
      max: 100,
      message: '分类名称长度应在1-100个字符之间',
      trigger: ['input', 'blur']
    }
  ],
  categoryDescription: [
    {
      max: 300,
      message: '描述长度不能超过300个字符',
      trigger: ['input', 'blur']
    }
  ]
}

// 方法
const applyCategory = (category: CategorySuggestion) => {
  formData.categoryName = category.name
  formData.categoryDescription = category.description
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true
    
    const submitData = {
      categoryName: formData.categoryName,
      categoryDescription: formData.categoryDescription || undefined
    }
    
    emit('save', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.add-category-form {
  width: 100%;
  max-width: 500px;
}

.form-header {
  margin-bottom: 20px;
  text-align: center;
}

.form-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.form-description {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.quick-categories {
  margin-top: 16px;
}

.categories-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.categories-label {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.categories-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.form-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

:deep(.n-form-item-label) {
  font-weight: 500;
}

:deep(.n-input) {
  border-radius: 6px;
}

:deep(.n-button--quaternary) {
  border: 1px solid #e0e0e0;
}

:deep(.n-button--quaternary:hover) {
  border-color: #1890ff;
  background-color: #f0f8ff;
}
</style>
