<template>
  <div class="dashboard-diet-tracker">
    <NCard class="tracker-card" title="今日饮食跟踪">
      <template #header-extra>
        <NButton text @click="$router.push('/diet-tracker')">
          查看详情
          <template #icon>
            <NIcon><ArrowForwardOutline /></NIcon>
          </template>
        </NButton>
      </template>

      <!-- 有计划的情况 -->
      <div v-if="todayPlan" class="tracker-content">
        <!-- 计划概览 -->
        <div class="plan-overview">
          <div class="plan-info">
            <h4>{{ todayPlan.planName || '今日饮食计划' }}</h4>
            <div v-if="selectedDietType" class="diet-type-badge">
              <div 
                class="color-indicator" 
                :style="{ backgroundColor: selectedDietType.color }"
              ></div>
              <span>{{ selectedDietType.name }}</span>
            </div>
          </div>
          <div class="progress-circle">
            <NProgress
              type="circle"
              :percentage="completionRate"
              :stroke-width="6"
              :show-indicator="false"
              style="width: 50px;"
            />
            <div class="progress-text">
              <span class="progress-number">{{ completionRate }}%</span>
            </div>
          </div>
        </div>

        <!-- 快速跟踪列表 -->
        <div class="quick-tracker">
          <div class="tracker-header">
            <span class="tracker-title">快速跟踪 ({{ consumedCount }}/{{ totalCount }})</span>
            <div class="filter-buttons">
              <NButton
                size="tiny"
                :type="showAll ? 'default' : 'primary'"
                @click="showAll = false"
              >
                待摄入
              </NButton>
              <NButton
                size="tiny"
                :type="showAll ? 'primary' : 'default'"
                @click="showAll = true"
              >
                全部
              </NButton>
            </div>
          </div>

          <div class="tracker-list">
            <div
              v-for="item in displayItems"
              :key="item.id"
              class="tracker-item"
              :class="{ consumed: item.isConsumed }"
            >
              <NCheckbox
                :checked="item.isConsumed"
                size="small"
                @update:checked="(checked) => toggleConsumption(item.id, checked)"
              />
              <div class="item-info">
                <span class="item-name">{{ item.categoryName }}</span>
                <span v-if="item.specificFoods && item.specificFoods.length > 0" class="item-count">
                  {{ item.specificFoods.length }}项
                </span>
              </div>
              <div v-if="item.isConsumed" class="consumed-indicator">
                <NIcon size="14" color="#52c41a">
                  <CheckmarkCircleOutline />
                </NIcon>
              </div>
            </div>
          </div>

          <!-- 完成祝贺 -->
          <div v-if="completionRate === 100" class="completion-celebration">
            <div class="celebration-content">
              <NIcon size="20" color="#52c41a">
                <CheckmarkCircleOutline />
              </NIcon>
              <span>今日饮食计划已完成！</span>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="quick-actions">
          <NButton size="small" @click="$router.push('/diet-plan')">
            <template #icon>
              <NIcon><CreateOutline /></NIcon>
            </template>
            编辑计划
          </NButton>
          <NButton size="small" @click="$router.push('/diet-tracker')">
            <template #icon>
              <NIcon><EyeOutline /></NIcon>
            </template>
            详细跟踪
          </NButton>
        </div>
      </div>

      <!-- 无计划的情况 -->
      <div v-else class="no-plan">
        <NEmpty size="small" description="今日暂无饮食计划">
          <template #icon>
            <NIcon size="40" color="#ccc">
              <RestaurantOutline />
            </NIcon>
          </template>
          <template #extra>
            <NButton size="small" type="primary" @click="$router.push('/diet-plan')">
              创建今日计划
            </NButton>
          </template>
        </NEmpty>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <NSpin size="small" />
        <span>加载中...</span>
      </div>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  NCard,
  NButton,
  NIcon,
  NProgress,
  NCheckbox,
  NEmpty,
  NSpin,
  useMessage
} from 'naive-ui'
import {
  ArrowForwardOutline,
  CheckmarkCircleOutline,
  CreateOutline,
  EyeOutline,
  RestaurantOutline
} from '@vicons/ionicons5'
import { useDailyDietPlanStore } from '../stores/dailyDietPlan'
import { useDietTypeStore } from '../stores/dietType'

const dailyPlanStore = useDailyDietPlanStore()
const dietTypeStore = useDietTypeStore()
const message = useMessage()

// 响应式状态
const showAll = ref(false)

// 计算属性
const todayPlan = computed(() => dailyPlanStore.getTodayPlan)
const todayPlanItems = computed(() => dailyPlanStore.getTodayPlanItems)
const loading = computed(() => dailyPlanStore.loading || dietTypeStore.loading)

const selectedDietType = computed(() => {
  if (!todayPlan.value?.dietTypeId) return null
  return dietTypeStore.getDietTypeById(todayPlan.value.dietTypeId)
})

const consumedCount = computed(() => {
  return todayPlanItems.value.filter(item => item.isConsumed).length
})

const totalCount = computed(() => todayPlanItems.value.length)

const completionRate = computed(() => {
  if (totalCount.value === 0) return 0
  return Math.round((consumedCount.value / totalCount.value) * 100)
})

const displayItems = computed(() => {
  if (showAll.value) {
    return todayPlanItems.value.slice(0, 6) // 最多显示6项
  } else {
    return todayPlanItems.value.filter(item => !item.isConsumed).slice(0, 4) // 最多显示4项待摄入
  }
})

// 方法
const toggleConsumption = async (itemId: number, isConsumed: boolean) => {
  try {
    await dailyPlanStore.toggleItemConsumption(itemId, isConsumed)
    message.success(isConsumed ? '已标记为摄入' : '已取消摄入标记')
  } catch (error) {
    message.error(error instanceof Error ? error.message : '操作失败')
  }
}

// 生命周期
onMounted(async () => {
  // 确保数据已加载
  if (dietTypeStore.dietTypes.length === 0) {
    await dietTypeStore.fetchDietTypes()
  }
  
  if (dailyPlanStore.dailyPlans.length === 0) {
    await dailyPlanStore.fetchDailyPlans(1) // 假设用户ID为1
  }
  
  const plan = todayPlan.value
  if (plan) {
    await dailyPlanStore.fetchPlanItems(plan.id)
  }
})
</script>

<style scoped>
.dashboard-diet-tracker {
  width: 100%;
}

.tracker-card {
  height: 100%;
}

.tracker-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.plan-overview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.plan-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.diet-type-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.color-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.progress-circle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-text {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-number {
  font-size: 10px;
  font-weight: 600;
  color: #333;
}

.quick-tracker {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tracker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tracker-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.filter-buttons {
  display: flex;
  gap: 4px;
}

.tracker-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  max-height: 200px;
  overflow-y: auto;
}

.tracker-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.tracker-item:hover {
  background-color: #f8f9fa;
}

.tracker-item.consumed {
  background-color: #f6ffed;
}

.item-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 6px;
}

.item-name {
  font-size: 13px;
  color: #333;
}

.item-count {
  font-size: 11px;
  color: #999;
  background: #f0f0f0;
  padding: 1px 4px;
  border-radius: 2px;
}

.consumed-indicator {
  flex-shrink: 0;
}

.completion-celebration {
  padding: 8px;
  background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  margin-top: 4px;
}

.celebration-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 12px;
  color: #52c41a;
  font-weight: 500;
}

.quick-actions {
  display: flex;
  gap: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.no-plan {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 100px;
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .plan-overview {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .progress-circle {
    align-self: center;
  }
  
  .quick-actions {
    flex-direction: column;
  }
}
</style>
