<template>
  <div class="food-categories-list">
    <div class="list-header">
      <div class="header-content">
        <h3>{{ dietType.name }} - 食物分类</h3>
        <p class="diet-description">{{ dietType.description }}</p>
      </div>
      <div class="header-actions">
        <NButton @click="showManager = !showManager">
          <template #icon>
            <NIcon><SettingsOutline /></NIcon>
          </template>
          {{ showManager ? '查看模式' : '管理模式' }}
        </NButton>
      </div>
    </div>

    <!-- 管理模式 -->
    <div v-if="showManager" class="manager-mode">
      <FoodCategoryManager
        :diet-type="dietType"
        :categories="categories"
      />
    </div>

    <!-- 查看模式 -->
    <div v-else class="view-mode">
      <div class="categories-container">
        <div
          v-for="(category, index) in sortedCategories"
          :key="category.id"
          class="category-item"
        >
          <div class="category-number">{{ index + 1 }}</div>
          <div class="category-content">
            <div class="category-header">
              <h4>{{ category.categoryName }}</h4>
              <span class="category-name-en" v-if="category.categoryNameEn">
                {{ category.categoryNameEn }}
              </span>
            </div>

            <p class="category-description">{{ category.description }}</p>

            <div class="category-details">
              <div class="detail-row">
                <div class="detail-item">
                  <NIcon class="detail-icon" size="16">
                    <RestaurantOutline />
                  </NIcon>
                  <div class="detail-content">
                    <span class="detail-label">食物示例</span>
                    <span class="detail-value">{{ category.examples }}</span>
                  </div>
                </div>
              </div>

              <div class="detail-row">
                <div class="detail-item">
                  <NIcon class="detail-icon" size="16">
                    <ScaleOutline />
                  </NIcon>
                  <div class="detail-content">
                    <span class="detail-label">推荐摄入量</span>
                    <span class="detail-value">{{ category.recommendedAmount }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="summary-section">
        <NCard class="summary-card" title="饮食指南总结">
          <div class="summary-content">
            <div class="summary-item">
              <span class="summary-label">食物分类总数：</span>
              <span class="summary-value">{{ categories.length }} 类</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">营养特点：</span>
              <span class="summary-value">{{ dietType.nutritionFeatures }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">主要益处：</span>
              <span class="summary-value">{{ dietType.benefits }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">适合人群：</span>
              <span class="summary-value">{{ dietType.suitablePeople }}</span>
            </div>
          </div>
        </NCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { NCard, NIcon, NButton } from 'naive-ui'
import { RestaurantOutline, ScaleOutline, SettingsOutline } from '@vicons/ionicons5'
import type { DietType, DietTypeFoodCategory } from '../stores/dietType'
import FoodCategoryManager from './FoodCategoryManager.vue'

interface Props {
  dietType: DietType
  categories: DietTypeFoodCategory[]
}

const props = defineProps<Props>()

// 响应式状态
const showManager = ref(false)

// 按排序顺序排列的分类
const sortedCategories = computed(() => {
  return [...props.categories].sort((a, b) => a.sortOrder - b.sortOrder)
})
</script>

<style scoped>
.food-categories-list {
  width: 100%;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-content {
  flex: 1;
}

.header-actions {
  flex-shrink: 0;
}

.list-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.diet-description {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.categories-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.category-item {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: #fafafa;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
  transition: all 0.2s ease;
}

.category-item:hover {
  background: #f5f5f5;
  border-color: #d0d0d0;
}

.category-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.category-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.category-header {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.category-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.category-name-en {
  font-size: 14px;
  color: #666;
  font-style: italic;
}

.category-description {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #555;
}

.category-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-row {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.detail-icon {
  color: #1890ff;
  margin-top: 2px;
  flex-shrink: 0;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.detail-label {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
}

.summary-section {
  margin-top: 32px;
}

.summary-card {
  background: linear-gradient(135deg, #f6f9fc 0%, #e9f4ff 100%);
  border: 1px solid #d6e7ff;
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.summary-label {
  font-size: 14px;
  font-weight: 600;
  color: #666;
}

.summary-value {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .category-item {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }
  
  .category-number {
    align-self: flex-start;
  }
  
  .detail-row {
    gap: 12px;
  }
  
  .detail-item {
    flex-direction: column;
    gap: 6px;
  }
  
  .detail-content {
    gap: 2px;
  }
}

@media (max-width: 480px) {
  .categories-container {
    gap: 12px;
  }
  
  .category-item {
    padding: 12px;
  }
  
  .category-header h4 {
    font-size: 16px;
  }
  
  .list-header h3 {
    font-size: 18px;
  }
}
</style>
