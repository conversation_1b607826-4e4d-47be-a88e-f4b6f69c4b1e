<template>
  <div class="food-category-form">
    <NForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <NGrid :cols="2" :x-gap="12">
        <NGridItem>
          <NFormItem label="分类名称" path="categoryName">
            <NInput
              v-model:value="formData.categoryName"
              placeholder="请输入分类名称"
              maxlength="100"
              show-count
            />
          </NFormItem>
        </NGridItem>
        <NGridItem>
          <NFormItem label="英文名称" path="categoryNameEn">
            <NInput
              v-model:value="formData.categoryNameEn"
              placeholder="请输入英文名称（可选）"
              maxlength="100"
              show-count
            />
          </NFormItem>
        </NGridItem>
      </NGrid>

      <NFormItem label="分类描述" path="description">
        <NInput
          v-model:value="formData.description"
          type="textarea"
          placeholder="请输入分类描述"
          :rows="2"
          maxlength="300"
          show-count
        />
      </NFormItem>

      <NFormItem label="食物示例" path="examples">
        <NInput
          v-model:value="formData.examples"
          type="textarea"
          placeholder="请输入食物示例，用逗号分隔"
          :rows="2"
          maxlength="500"
          show-count
        />
      </NFormItem>

      <NFormItem label="推荐摄入量" path="recommendedAmount">
        <NInput
          v-model:value="formData.recommendedAmount"
          placeholder="请输入推荐摄入量，如'每日200-300g'"
          maxlength="100"
          show-count
        />
      </NFormItem>

      <NFormItem label="排序顺序" path="sortOrder">
        <NInputNumber
          v-model:value="formData.sortOrder"
          placeholder="排序顺序"
          :min="0"
          :max="999"
          style="width: 100%;"
        />
      </NFormItem>

      <!-- 快速选择模板 -->
      <div v-if="!isEditing" class="quick-templates">
        <NFormItem label="快速选择模板">
          <div class="templates-container">
            <p class="templates-label">常见食物分类：</p>
            <div class="templates-list">
              <NButton
                v-for="template in categoryTemplates"
                :key="template.categoryName"
                size="small"
                quaternary
                type="info"
                @click="applyTemplate(template)"
              >
                {{ template.categoryName }}
              </NButton>
            </div>
          </div>
        </NFormItem>
      </div>
    </NForm>

    <div class="form-actions">
      <NSpace>
        <NButton @click="handleCancel">取消</NButton>
        <NButton
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ isEditing ? '更新' : '添加' }}
        </NButton>
      </NSpace>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import {
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NButton,
  NSpace,
  NGrid,
  NGridItem,
  type FormInst,
  type FormRules
} from 'naive-ui'
import type { DietType, DietTypeFoodCategory } from '../stores/dietType'

interface Props {
  dietType: DietType
  category?: DietTypeFoodCategory | null
}

interface Emits {
  (e: 'save', data: Omit<DietTypeFoodCategory, 'id' | 'dietTypeId' | 'createdAt'>): void
  (e: 'cancel'): void
}

interface CategoryTemplate {
  categoryName: string
  categoryNameEn?: string
  description: string
  examples: string
  recommendedAmount: string
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInst | null>(null)
const loading = ref(false)

const isEditing = computed(() => !!props.category?.id)

// 表单数据
const formData = reactive({
  categoryName: '',
  categoryNameEn: '',
  description: '',
  examples: '',
  recommendedAmount: '',
  sortOrder: 0
})

// 分类模板
const categoryTemplates: CategoryTemplate[] = [
  {
    categoryName: '蔬菜',
    categoryNameEn: 'Vegetables',
    description: '新鲜的各类蔬菜，富含维生素、矿物质和膳食纤维',
    examples: '西红柿、黄瓜、菠菜、茄子、洋葱、胡萝卜',
    recommendedAmount: '每日400-500g'
  },
  {
    categoryName: '水果',
    categoryNameEn: 'Fruits',
    description: '新鲜的季节性水果，提供维生素C和抗氧化物质',
    examples: '橙子、苹果、葡萄、香蕉、柠檬',
    recommendedAmount: '每日200-300g'
  },
  {
    categoryName: '全谷物',
    categoryNameEn: 'Whole Grains',
    description: '未精制的谷物，提供复合碳水化合物和B族维生素',
    examples: '全麦面包、糙米、燕麦、大麦',
    recommendedAmount: '每日150-200g'
  },
  {
    categoryName: '蛋白质',
    categoryNameEn: 'Protein',
    description: '高质量蛋白质来源',
    examples: '鸡肉、牛肉、鱼肉、鸡蛋、豆腐',
    recommendedAmount: '每餐20-30g'
  },
  {
    categoryName: '坚果种子',
    categoryNameEn: 'Nuts and Seeds',
    description: '各种坚果和种子，提供健康脂肪和蛋白质',
    examples: '核桃、杏仁、榛子、亚麻籽、奇亚籽',
    recommendedAmount: '每日30g'
  },
  {
    categoryName: '乳制品',
    categoryNameEn: 'Dairy',
    description: '乳制品，提供钙质和蛋白质',
    examples: '牛奶、酸奶、奶酪',
    recommendedAmount: '每日2-3份'
  },
  {
    categoryName: '健康油脂',
    categoryNameEn: 'Healthy Fats',
    description: '单不饱和和多不饱和脂肪',
    examples: '橄榄油、牛油果、坚果油',
    recommendedAmount: '每日适量'
  },
  {
    categoryName: '饮品',
    categoryNameEn: 'Beverages',
    description: '健康的饮品选择',
    examples: '绿茶、白开水、柠檬水',
    recommendedAmount: '每日充足'
  }
]

// 表单验证规则
const rules: FormRules = {
  categoryName: [
    {
      required: true,
      message: '请输入分类名称',
      trigger: ['input', 'blur']
    },
    {
      min: 1,
      max: 100,
      message: '分类名称长度应在1-100个字符之间',
      trigger: ['input', 'blur']
    }
  ],
  categoryNameEn: [
    {
      max: 100,
      message: '英文名称长度不能超过100个字符',
      trigger: ['input', 'blur']
    }
  ],
  description: [
    {
      required: true,
      message: '请输入分类描述',
      trigger: ['input', 'blur']
    },
    {
      max: 300,
      message: '描述长度不能超过300个字符',
      trigger: ['input', 'blur']
    }
  ],
  examples: [
    {
      required: true,
      message: '请输入食物示例',
      trigger: ['input', 'blur']
    },
    {
      max: 500,
      message: '食物示例长度不能超过500个字符',
      trigger: ['input', 'blur']
    }
  ],
  recommendedAmount: [
    {
      required: true,
      message: '请输入推荐摄入量',
      trigger: ['input', 'blur']
    },
    {
      max: 100,
      message: '推荐摄入量长度不能超过100个字符',
      trigger: ['input', 'blur']
    }
  ],
  sortOrder: [
    {
      type: 'number',
      required: true,
      message: '请输入排序顺序',
      trigger: ['input', 'blur']
    }
  ]
}

// 监听props变化，更新表单数据
watch(
  () => props.category,
  (newCategory) => {
    if (newCategory) {
      Object.assign(formData, {
        categoryName: newCategory.categoryName || '',
        categoryNameEn: newCategory.categoryNameEn || '',
        description: newCategory.description || '',
        examples: newCategory.examples || '',
        recommendedAmount: newCategory.recommendedAmount || '',
        sortOrder: newCategory.sortOrder || 0
      })
    } else {
      // 重置表单
      Object.assign(formData, {
        categoryName: '',
        categoryNameEn: '',
        description: '',
        examples: '',
        recommendedAmount: '',
        sortOrder: 0
      })
    }
  },
  { immediate: true }
)

// 方法
const applyTemplate = (template: CategoryTemplate) => {
  formData.categoryName = template.categoryName
  formData.categoryNameEn = template.categoryNameEn || ''
  formData.description = template.description
  formData.examples = template.examples
  formData.recommendedAmount = template.recommendedAmount
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true
    
    const submitData = {
      categoryName: formData.categoryName,
      categoryNameEn: formData.categoryNameEn || undefined,
      description: formData.description,
      examples: formData.examples,
      recommendedAmount: formData.recommendedAmount,
      sortOrder: formData.sortOrder
    }
    
    emit('save', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.food-category-form {
  width: 100%;
  max-width: 600px;
}

.quick-templates {
  margin-top: 16px;
}

.templates-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.templates-label {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.templates-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.form-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

:deep(.n-form-item-label) {
  font-weight: 500;
}

:deep(.n-input) {
  border-radius: 6px;
}

:deep(.n-input-number) {
  border-radius: 6px;
}

:deep(.n-button--quaternary) {
  border: 1px solid #e0e0e0;
}

:deep(.n-button--quaternary:hover) {
  border-color: #1890ff;
  background-color: #f0f8ff;
}
</style>
