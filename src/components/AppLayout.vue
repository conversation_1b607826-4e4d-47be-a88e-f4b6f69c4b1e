<template>
  <!-- 桌面端布局 -->
  <NLayout v-if="!isMobile" has-sider>
    <NLayoutSider
      bordered
      collapse-mode="width"
      :collapsed-width="64"
      :width="240"
      :collapsed="collapsed"
      show-trigger
      @collapse="collapsed = true"
      @expand="collapsed = false"
    >
      <NMenu
        :collapsed="collapsed"
        :collapsed-width="64"
        :collapsed-icon-size="22"
        :options="menuOptions"
        :value="activeKey"
        @update:value="handleMenuSelect"
      />
    </NLayoutSider>
    <NLayout>
      <NLayoutHeader bordered style="height: 64px; padding: 0 24px; display: flex; align-items: center; justify-content: space-between;">
        <h2 style="margin: 0;">{{ currentTitle }}</h2>
        <NSpace>
          <NButton quaternary circle>
            <template #icon>
              <NIcon><PersonOutline /></NIcon>
            </template>
          </NButton>
        </NSpace>
      </NLayoutHeader>
      <NLayoutContent class="desktop-content">
        <RouterView />
      </NLayoutContent>
    </NLayout>
  </NLayout>

  <!-- 移动端布局 -->
  <NLayout v-else class="mobile-layout">
    <NLayoutHeader bordered class="mobile-header">
      <div class="mobile-header-content">
        <NButton
          quaternary
          circle
          @click="showMobileMenu = true"
          class="menu-button"
        >
          <template #icon>
            <NIcon><MenuOutline /></NIcon>
          </template>
        </NButton>
        <h3 class="mobile-title">{{ currentTitle }}</h3>
        <NButton quaternary circle>
          <template #icon>
            <NIcon><PersonOutline /></NIcon>
          </template>
        </NButton>
      </div>
    </NLayoutHeader>

    <NLayoutContent class="mobile-content">
      <div class="mobile-content-wrapper">
        <RouterView />
      </div>
    </NLayoutContent>

    <!-- 移动端底部导航 -->
    <div class="mobile-bottom-nav">
      <div
        v-for="item in bottomNavItems"
        :key="item.key"
        class="nav-item"
        :class="{ active: activeKey === item.key }"
        @click="handleMenuSelect(item.key)"
      >
        <NIcon size="20" class="nav-icon">
          <component :is="item.icon" />
        </NIcon>
        <span class="nav-label">{{ item.label }}</span>
      </div>
    </div>

    <!-- 移动端侧边菜单 -->
    <NDrawer v-model:show="showMobileMenu" :width="280" placement="left">
      <NDrawerContent title="菜单" closable>
        <NMenu
          :options="menuOptions"
          :value="activeKey"
          @update:value="handleMobileMenuSelect"
        />
      </NDrawerContent>
    </NDrawer>
  </NLayout>
</template>

<script setup lang="ts">
import { ref, computed, h, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  NLayout,
  NLayoutSider,
  NLayoutHeader,
  NLayoutContent,
  NMenu,
  NButton,
  NSpace,
  NIcon,
  NDrawer,
  NDrawerContent,
  type MenuOption
} from 'naive-ui'
import {
  RestaurantOutline,
  DocumentTextOutline,
  PricetagsOutline,
  PersonOutline,
  StatsChartOutline,
  MenuOutline,
  NutritionOutline,
  CalendarOutline,
  CheckboxOutline,
  ListOutline,
  TodayOutline
} from '@vicons/ionicons5'

const router = useRouter()
const route = useRoute()

const collapsed = ref(false)
const showMobileMenu = ref(false)
const isMobile = ref(false)

const activeKey = computed(() => route.name as string)
const currentTitle = computed(() => route.meta?.title as string || '营养追踪器')

const menuOptions: MenuOption[] = [
  {
    label: '仪表盘',
    key: 'Dashboard',
    icon: () => h(NIcon, null, { default: () => h(StatsChartOutline) })
  },
  {
    label: '食物库',
    key: 'Foods',
    icon: () => h(NIcon, null, { default: () => h(RestaurantOutline) })
  },
  {
    label: '营养记录',
    key: 'Records',
    icon: () => h(NIcon, null, { default: () => h(DocumentTextOutline) })
  },
  {
    label: '饮食类型',
    key: 'DietTypes',
    icon: () => h(NIcon, null, { default: () => h(ListOutline) })
  },
  {
    label: '饮食计划',
    key: 'DailyDietPlan',
    icon: () => h(NIcon, null, { default: () => h(CalendarOutline) })
  },
  {
    label: '饮食跟踪',
    key: 'DailyDietTracker',
    icon: () => h(NIcon, null, { default: () => h(TodayOutline) })
  },
  {
    label: '标签管理',
    key: 'Tags',
    icon: () => h(NIcon, null, { default: () => h(PricetagsOutline) })
  },
  {
    label: '个人资料',
    key: 'Profile',
    icon: () => h(NIcon, null, { default: () => h(PersonOutline) })
  }
]

// 移动端底部导航项
const bottomNavItems = [
  {
    label: '仪表盘',
    key: 'Dashboard',
    icon: StatsChartOutline
  },
  {
    label: '饮食跟踪',
    key: 'DailyDietTracker',
    icon: TodayOutline
  },
  {
    label: '饮食计划',
    key: 'DailyDietPlan',
    icon: CalendarOutline
  },
  {
    label: '食物库',
    key: 'Foods',
    icon: RestaurantOutline
  },
  {
    label: '个人',
    key: 'Profile',
    icon: PersonOutline
  }
]

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const handleMenuSelect = (key: string) => {
  router.push({ name: key })
}

const handleMobileMenuSelect = (key: string) => {
  showMobileMenu.value = false
  router.push({ name: key })
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.n-layout {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* 移动端样式 */
.mobile-layout {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
}

.mobile-header {
  height: 56px;
  padding: 0;
  flex-shrink: 0;
}

.mobile-header-content {
  height: 100%;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mobile-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.desktop-content {
  padding: 24px 24px 24px 24px;
  width: 100%;
  max-width: none;
  flex: 1;
  overflow-y: auto;
  box-sizing: border-box;
}

.mobile-content {
  flex: 1;
  padding: 0;
  padding-bottom: 80px; /* 为底部导航留出空间 */
  overflow-y: auto;
  width: 100%;
}

.mobile-content-wrapper {
  padding: 16px 16px 16px 16px;
  width: 100%;
  min-height: 100%;
  box-sizing: border-box;
}

.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 64px;
  background: white;
  border-top: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: 1000;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 60px;
}

.nav-item:hover {
  background-color: #f5f5f5;
}

.nav-item.active {
  color: #18a058;
}

.nav-item.active .nav-icon {
  color: #18a058;
}

.nav-icon {
  margin-bottom: 2px;
}

.nav-label {
  font-size: 12px;
  line-height: 1;
}

.menu-button {
  margin-right: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .mobile-content-wrapper {
    padding: 12px;
  }

  .mobile-header-content {
    padding: 0 12px;
  }
}

@media (max-width: 480px) {
  .nav-label {
    font-size: 11px;
  }

  .nav-item {
    min-width: 50px;
    padding: 6px;
  }
}
</style>
