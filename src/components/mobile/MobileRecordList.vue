<template>
  <div class="mobile-record-list">
    <!-- 空状态 -->
    <div v-if="records.length === 0" class="empty-state">
      <NEmpty description="暂无营养记录">
        <template #extra>
          <NButton @click="$emit('add-record')">添加第一条记录</NButton>
        </template>
      </NEmpty>
    </div>

    <!-- 记录列表 -->
    <div v-else class="record-list">
      <!-- 按日期分组显示 -->
      <div
        v-for="(dayRecords, date) in groupedRecords"
        :key="date"
        class="day-group"
      >
        <div class="day-header">
          <h3 class="day-title">{{ formatDate(date) }}</h3>
          <div class="day-summary">
            <span class="day-calories">{{ getDayTotalCalories(dayRecords) }} 千卡</span>
          </div>
        </div>

        <div class="day-records">
          <div
            v-for="record in dayRecords"
            :key="record.id"
            class="record-card"
            @click="$emit('view-record', record)"
          >
            <div class="record-header">
              <div class="record-time">
                <NIcon class="time-icon"><TimeOutline /></NIcon>
                {{ record.record_time }}
              </div>
              <div class="record-actions">
                <NButton
                  size="small"
                  quaternary
                  circle
                  @click.stop="$emit('edit-record', record)"
                >
                  <template #icon>
                    <NIcon><CreateOutline /></NIcon>
                  </template>
                </NButton>
                <NButton
                  size="small"
                  quaternary
                  circle
                  type="error"
                  @click.stop="$emit('delete-record', record)"
                >
                  <template #icon>
                    <NIcon><TrashOutline /></NIcon>
                  </template>
                </NButton>
              </div>
            </div>

            <div class="record-content">
              <div class="food-info">
                <h4 class="food-name">{{ record.food?.name || record.food_name }}</h4>
                <p class="food-quantity">{{ record.quantity }}{{ record.unit }}</p>
              </div>

              <div class="nutrition-summary">
                <div class="nutrition-item primary">
                  <span class="nutrition-label">热量</span>
                  <span class="nutrition-value">{{ record.actual_calories }} 千卡</span>
                </div>
                <div class="nutrition-grid">
                  <div class="nutrition-item">
                    <span class="nutrition-label">蛋白质</span>
                    <span class="nutrition-value">{{ record.actual_protein }}g</span>
                  </div>
                  <div class="nutrition-item">
                    <span class="nutrition-label">碳水</span>
                    <span class="nutrition-value">{{ record.actual_carbohydrates }}g</span>
                  </div>
                  <div class="nutrition-item">
                    <span class="nutrition-label">脂肪</span>
                    <span class="nutrition-value">{{ record.actual_total_fat }}g</span>
                  </div>
                </div>
              </div>
            </div>

            <div v-if="record.notes" class="record-notes">
              <NIcon class="notes-icon"><DocumentTextOutline /></NIcon>
              {{ record.notes }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import dayjs from 'dayjs'
import type { NutritionRecord } from '../../stores/nutrition'
import {
  TimeOutline,
  CreateOutline,
  TrashOutline,
  DocumentTextOutline
} from '@vicons/ionicons5'

interface Props {
  records: NutritionRecord[]
}

const props = defineProps<Props>()

defineEmits<{
  'add-record': []
  'view-record': [record: NutritionRecord]
  'edit-record': [record: NutritionRecord]
  'delete-record': [record: NutritionRecord]
}>()

// 按日期分组记录
const groupedRecords = computed(() => {
  const groups: Record<string, NutritionRecord[]> = {}
  
  props.records.forEach(record => {
    const date = record.record_date
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(record)
  })
  
  // 按时间排序每天的记录
  Object.keys(groups).forEach(date => {
    groups[date].sort((a, b) => a.record_time.localeCompare(b.record_time))
  })
  
  return groups
})

// 格式化日期显示
const formatDate = (dateStr: string) => {
  const date = dayjs(dateStr)
  const today = dayjs()
  const yesterday = today.subtract(1, 'day')
  
  if (date.isSame(today, 'day')) {
    return '今天'
  } else if (date.isSame(yesterday, 'day')) {
    return '昨天'
  } else {
    return date.format('MM月DD日')
  }
}

// 计算每天总热量
const getDayTotalCalories = (dayRecords: NutritionRecord[]) => {
  return dayRecords.reduce((total, record) => total + record.actual_calories, 0)
}
</script>

<style scoped>
.mobile-record-list {
  width: 100%;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.record-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.day-group {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.day-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.day-summary {
  display: flex;
  align-items: center;
}

.day-calories {
  font-size: 14px;
  font-weight: 500;
  color: #f56565;
}

.day-records {
  display: flex;
  flex-direction: column;
}

.record-card {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.record-card:last-child {
  border-bottom: none;
}

.record-card:hover {
  background-color: #fafafa;
}

.record-card:active {
  background-color: #f0f0f0;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.record-time {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.time-icon {
  font-size: 14px;
}

.record-actions {
  display: flex;
  gap: 4px;
}

.record-content {
  margin-bottom: 12px;
}

.food-info {
  margin-bottom: 12px;
}

.food-name {
  margin: 0 0 4px 0;
  font-size: 15px;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.food-quantity {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.nutrition-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.nutrition-item.primary {
  padding: 8px 12px;
  background: #f56565;
  color: white;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nutrition-item.primary .nutrition-label {
  color: rgba(255, 255, 255, 0.9);
}

.nutrition-item.primary .nutrition-value {
  color: white;
  font-weight: 600;
}

.nutrition-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
}

.nutrition-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.nutrition-label {
  font-size: 11px;
  color: #666;
  margin-bottom: 2px;
}

.nutrition-value {
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

.record-notes {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.notes-icon {
  font-size: 14px;
  margin-top: 1px;
  flex-shrink: 0;
}

/* 小屏幕优化 */
@media (max-width: 480px) {
  .day-group {
    border-radius: 8px;
  }
  
  .day-header {
    padding: 12px;
  }
  
  .day-title {
    font-size: 15px;
  }
  
  .day-calories {
    font-size: 13px;
  }
  
  .record-card {
    padding: 12px;
  }
  
  .food-name {
    font-size: 14px;
  }
  
  .nutrition-grid {
    gap: 6px;
  }
}
</style>
