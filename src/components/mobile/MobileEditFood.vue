<template>
  <div class="mobile-edit-food">
    <!-- 顶部导航栏 -->
    <div class="mobile-header">
      <NButton quaternary circle @click="$emit('cancel')">
        <template #icon>
          <NIcon><ArrowBackOutline /></NIcon>
        </template>
      </NButton>
      <h2 class="header-title">编辑食物</h2>
      <NButton 
        type="primary" 
        size="small" 
        @click="$emit('save')"
        :loading="loading"
      >
        保存
      </NButton>
    </div>

    <!-- 表单内容 -->
    <div class="mobile-form-content">
      <NForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="top"
        require-mark-placement="right-hanging"
      >
        <!-- 基本信息卡片 -->
        <NCard title="基本信息" size="small" :bordered="false" class="form-card">
          <NFormItem label="食物名称" path="name">
            <NInput
              v-model:value="formData.name"
              placeholder="请输入食物名称"
              clearable
            />
          </NFormItem>

          <NFormItem label="品牌" path="brand">
            <NInput
              v-model:value="formData.brand"
              placeholder="品牌（可选）"
              clearable
            />
          </NFormItem>

          <NFormItem label="条形码" path="barcode">
            <NInput
              v-model:value="formData.barcode"
              placeholder="条形码（可选）"
              clearable
            />
          </NFormItem>
        </NCard>

        <!-- 营养成分卡片 -->
        <NCard title="营养成分 (每100g)" size="small" :bordered="false" class="form-card">
          <div class="nutrition-grid">
            <!-- 热量 -->
            <div class="nutrition-item">
              <label class="nutrition-label">热量 (千卡)</label>
              <NInputNumber
                v-model:value="formData.calories"
                :precision="2"
                :min="0"
                placeholder="0"
                class="nutrition-input"
              />
            </div>

            <!-- 蛋白质 -->
            <div class="nutrition-item">
              <label class="nutrition-label">蛋白质 (克)</label>
              <NInputNumber
                v-model:value="formData.protein"
                :precision="2"
                :min="0"
                placeholder="0"
                class="nutrition-input"
              />
            </div>

            <!-- 碳水化合物 -->
            <div class="nutrition-item">
              <label class="nutrition-label">碳水化合物 (克)</label>
              <NInputNumber
                v-model:value="formData.carbohydrates"
                :precision="2"
                :min="0"
                placeholder="0"
                class="nutrition-input"
              />
            </div>

            <!-- 脂肪 -->
            <div class="nutrition-item">
              <label class="nutrition-label">脂肪 (克)</label>
              <NInputNumber
                v-model:value="formData.total_fat"
                :precision="2"
                :min="0"
                placeholder="0"
                class="nutrition-input"
              />
            </div>
          </div>

          <!-- 展开更多营养成分 -->
          <div class="expand-section">
            <NButton
              text
              type="primary"
              @click="showMoreNutrition = !showMoreNutrition"
              class="expand-button"
            >
              <template #icon>
                <NIcon>
                  <ChevronDownOutline v-if="!showMoreNutrition" />
                  <ChevronUpOutline v-else />
                </NIcon>
              </template>
              {{ showMoreNutrition ? '收起详细营养成分' : '展开详细营养成分' }}
            </NButton>
          </div>

          <!-- 详细营养成分 -->
          <div v-if="showMoreNutrition" class="more-nutrition">
            <div class="nutrition-grid">
              <div class="nutrition-item">
                <label class="nutrition-label">饱和脂肪 (克)</label>
                <NInputNumber
                  v-model:value="formData.saturated_fat"
                  :precision="2"
                  :min="0"
                  placeholder="0"
                  class="nutrition-input"
                />
              </div>

              <div class="nutrition-item">
                <label class="nutrition-label">钠 (毫克)</label>
                <NInputNumber
                  v-model:value="formData.sodium"
                  :precision="2"
                  :min="0"
                  placeholder="0"
                  class="nutrition-input"
                />
              </div>

              <div class="nutrition-item">
                <label class="nutrition-label">膳食纤维 (克)</label>
                <NInputNumber
                  v-model:value="formData.dietary_fiber"
                  :precision="2"
                  :min="0"
                  placeholder="0"
                  class="nutrition-input"
                />
              </div>

              <div class="nutrition-item">
                <label class="nutrition-label">糖类 (克)</label>
                <NInputNumber
                  v-model:value="formData.sugar"
                  :precision="2"
                  :min="0"
                  placeholder="0"
                  class="nutrition-input"
                />
              </div>

              <div class="nutrition-item">
                <label class="nutrition-label">维生素C (毫克)</label>
                <NInputNumber
                  v-model:value="formData.vitamin_c"
                  :precision="2"
                  :min="0"
                  placeholder="0"
                  class="nutrition-input"
                />
              </div>

              <div class="nutrition-item">
                <label class="nutrition-label">钙 (毫克)</label>
                <NInputNumber
                  v-model:value="formData.calcium"
                  :precision="2"
                  :min="0"
                  placeholder="0"
                  class="nutrition-input"
                />
              </div>
            </div>
          </div>
        </NCard>

        <!-- 其他信息卡片 -->
        <NCard title="其他信息" size="small" :bordered="false" class="form-card">
          <NFormItem label="描述">
            <NInput
              v-model:value="formData.description"
              type="textarea"
              placeholder="食物描述（可选）"
              :rows="3"
            />
          </NFormItem>
        </NCard>
      </NForm>
    </div>

    <!-- 底部操作栏 -->
    <div class="mobile-footer">
      <NSpace justify="space-between">
        <NButton @click="$emit('cancel')" size="large" style="flex: 1;">
          取消
        </NButton>
        <NButton 
          type="primary" 
          @click="$emit('save')" 
          :loading="loading"
          size="large"
          style="flex: 1; margin-left: 12px;"
        >
          保存修改
        </NButton>
      </NSpace>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  NButton,
  NIcon,
  NCard,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NSpace,
  type FormInst,
  type FormRules
} from 'naive-ui'
import {
  ArrowBackOutline,
  ChevronDownOutline,
  ChevronUpOutline
} from '@vicons/ionicons5'

interface Props {
  formData: any
  rules: FormRules
  loading?: boolean
}

defineProps<Props>()

defineEmits<{
  'cancel': []
  'save': []
}>()

const formRef = ref<FormInst | null>(null)
const showMoreNutrition = ref(false)
</script>

<style scoped>
.mobile-edit-food {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.mobile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.mobile-form-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  padding-bottom: 80px; /* 为底部操作栏留出空间 */
}

.form-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.nutrition-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.nutrition-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.nutrition-label {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.nutrition-input {
  width: 100%;
}

.expand-section {
  margin-top: 16px;
  text-align: center;
}

.expand-button {
  width: 100%;
  justify-content: center;
}

.more-nutrition {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.mobile-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background-color: white;
  border-top: 1px solid #e0e0e0;
  z-index: 100;
}

/* 表单项样式优化 */
:deep(.n-form-item) {
  margin-bottom: 16px;
}

:deep(.n-form-item-label) {
  font-weight: 500;
  color: #333;
}

:deep(.n-input) {
  border-radius: 8px;
}

:deep(.n-input-number) {
  border-radius: 8px;
}

:deep(.n-card__content) {
  padding: 16px;
}

:deep(.n-card-header) {
  padding: 16px 16px 0 16px;
}

:deep(.n-card-header__main) {
  font-weight: 600;
  color: #333;
}
</style>
