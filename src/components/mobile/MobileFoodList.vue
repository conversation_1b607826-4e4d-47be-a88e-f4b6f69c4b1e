<template>
  <div class="mobile-food-list">
    <!-- 空状态 -->
    <div v-if="foods.length === 0" class="empty-state">
      <NEmpty description="暂无食物数据">
        <template #extra>
          <NButton @click="$emit('add-food')">添加第一个食物</NButton>
        </template>
      </NEmpty>
    </div>

    <!-- 食物列表 -->
    <div v-else class="food-list">
      <div
        v-for="food in foods"
        :key="food.id"
        class="food-card"
        @click="$emit('view-food', food)"
      >
        <div class="food-header">
          <div class="food-info">
            <h3 class="food-name">{{ food.name }}</h3>
            <p v-if="food.brand" class="food-brand">{{ food.brand }}</p>
          </div>
          <div class="food-actions">
            <NButton
              size="small"
              quaternary
              circle
              @click.stop="$emit('edit-food', food)"
            >
              <template #icon>
                <NIcon><CreateOutline /></NIcon>
              </template>
            </NButton>
            <NButton
              size="small"
              quaternary
              circle
              type="error"
              @click.stop="$emit('delete-food', food)"
            >
              <template #icon>
                <NIcon><TrashOutline /></NIcon>
              </template>
            </NButton>
          </div>
        </div>

        <div class="food-nutrition">
          <div class="nutrition-item">
            <span class="nutrition-label">热量</span>
            <span class="nutrition-value">{{ food.calories }} 千卡</span>
          </div>
          <div class="nutrition-item">
            <span class="nutrition-label">蛋白质</span>
            <span class="nutrition-value">{{ food.protein }}g</span>
          </div>
          <div class="nutrition-item">
            <span class="nutrition-label">碳水</span>
            <span class="nutrition-value">{{ food.carbohydrates }}g</span>
          </div>
          <div class="nutrition-item">
            <span class="nutrition-label">脂肪</span>
            <span class="nutrition-value">{{ food.total_fat }}g</span>
          </div>
        </div>

        <div v-if="food.tags && food.tags.length > 0" class="food-tags">
          <NTag
            v-for="tag in food.tags"
            :key="tag.id"
            size="small"
            :color="{ color: tag.color, textColor: '#fff' }"
          >
            {{ tag.name }}
          </NTag>
        </div>

        <div v-if="food.description" class="food-description">
          {{ food.description }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Food } from '../../stores/food'
import { CreateOutline, TrashOutline } from '@vicons/ionicons5'

interface Props {
  foods: Food[]
}

defineProps<Props>()

defineEmits<{
  'add-food': []
  'view-food': [food: Food]
  'edit-food': [food: Food]
  'delete-food': [food: Food]
}>()
</script>

<style scoped>
.mobile-food-list {
  width: 100%;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.food-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.food-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.food-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.food-card:active {
  transform: translateY(0);
}

.food-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.food-info {
  flex: 1;
  min-width: 0;
}

.food-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.food-brand {
  margin: 0;
  font-size: 12px;
  color: #666;
  line-height: 1.2;
}

.food-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
  margin-left: 12px;
}

.food-nutrition {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 16px;
  margin-bottom: 12px;
}

.nutrition-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nutrition-label {
  font-size: 12px;
  color: #666;
}

.nutrition-value {
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.food-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 8px;
}

.food-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

/* 小屏幕优化 */
@media (max-width: 480px) {
  .food-card {
    padding: 12px;
    border-radius: 8px;
  }
  
  .food-name {
    font-size: 15px;
  }
  
  .food-nutrition {
    gap: 6px 12px;
  }
  
  .nutrition-label {
    font-size: 11px;
  }
  
  .nutrition-value {
    font-size: 12px;
  }
}
</style>
