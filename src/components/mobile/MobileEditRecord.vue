<template>
  <div class="mobile-edit-record">
    <!-- 顶部导航栏 -->
    <div class="mobile-header">
      <NButton quaternary circle @click="$emit('cancel')">
        <template #icon>
          <NIcon><ArrowBackOutline /></NIcon>
        </template>
      </NButton>
      <h2 class="header-title">编辑记录</h2>
      <NButton 
        type="primary" 
        size="small" 
        @click="$emit('save')"
        :loading="loading"
      >
        保存
      </NButton>
    </div>

    <!-- 表单内容 -->
    <div class="mobile-form-content">
      <NForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="top"
        require-mark-placement="right-hanging"
      >
        <!-- 时间信息卡片 -->
        <NCard title="记录时间" size="small" :bordered="false" class="form-card">
          <NFormItem label="日期" path="record_date">
            <NDatePicker
              v-model:value="formData.record_date"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
            />
          </NFormItem>

          <NFormItem label="时间" path="record_time">
            <NTimePicker
              v-model:value="formData.record_time"
              format="HH:mm"
              placeholder="选择时间"
              style="width: 100%"
            />
          </NFormItem>
        </NCard>

        <!-- 食物选择卡片 -->
        <NCard title="食物信息" size="small" :bordered="false" class="form-card">
          <NFormItem label="食物类型" path="food_type">
            <NRadioGroup v-model:value="formData.food_type" @update:value="$emit('food-type-change')">
              <div class="radio-group">
                <NRadio value="existing" class="radio-item">
                  <div class="radio-content">
                    <NIcon class="radio-icon"><RestaurantOutline /></NIcon>
                    <span>选择已有食物</span>
                  </div>
                </NRadio>
                <NRadio value="custom" class="radio-item">
                  <div class="radio-content">
                    <NIcon class="radio-icon"><CreateOutline /></NIcon>
                    <span>自定义食物</span>
                  </div>
                </NRadio>
              </div>
            </NRadioGroup>
          </NFormItem>

          <!-- 选择已有食物 -->
          <div v-if="formData.food_type === 'existing'">
            <NFormItem label="选择食物" path="food_id">
              <NSelect
                v-model:value="formData.food_id"
                :options="foodOptions"
                placeholder="搜索并选择食物"
                filterable
                clearable
                @update:value="$emit('food-change')"
              />
            </NFormItem>
          </div>

          <!-- 自定义食物 -->
          <div v-if="formData.food_type === 'custom'">
            <NFormItem label="食物名称" path="food_name">
              <NInput
                v-model:value="formData.food_name"
                placeholder="请输入食物名称"
                clearable
              />
            </NFormItem>

            <!-- 自定义营养成分 -->
            <div class="custom-nutrition">
              <h4 class="section-title">营养成分 (每100g)</h4>
              <div class="nutrition-grid">
                <div class="nutrition-item">
                  <label class="nutrition-label">热量 (千卡)</label>
                  <NInputNumber
                    v-model:value="formData.custom_nutrition.calories"
                    :precision="2"
                    :min="0"
                    placeholder="0"
                    class="nutrition-input"
                  />
                </div>

                <div class="nutrition-item">
                  <label class="nutrition-label">蛋白质 (克)</label>
                  <NInputNumber
                    v-model:value="formData.custom_nutrition.protein"
                    :precision="2"
                    :min="0"
                    placeholder="0"
                    class="nutrition-input"
                  />
                </div>

                <div class="nutrition-item">
                  <label class="nutrition-label">碳水化合物 (克)</label>
                  <NInputNumber
                    v-model:value="formData.custom_nutrition.carbohydrates"
                    :precision="2"
                    :min="0"
                    placeholder="0"
                    class="nutrition-input"
                  />
                </div>

                <div class="nutrition-item">
                  <label class="nutrition-label">脂肪 (克)</label>
                  <NInputNumber
                    v-model:value="formData.custom_nutrition.fat"
                    :precision="2"
                    :min="0"
                    placeholder="0"
                    class="nutrition-input"
                  />
                </div>
              </div>
            </div>
          </div>
        </NCard>

        <!-- 食用量卡片 -->
        <NCard title="食用量" size="small" :bordered="false" class="form-card">
          <div class="quantity-grid">
            <NFormItem label="数量" path="quantity">
              <NInputNumber
                v-model:value="formData.quantity"
                :precision="2"
                :min="0.1"
                placeholder="数量"
                style="width: 100%"
              />
            </NFormItem>

            <NFormItem label="单位" path="unit">
              <NSelect
                v-model:value="formData.unit"
                :options="unitOptions"
                placeholder="选择单位"
              />
            </NFormItem>
          </div>
        </NCard>

        <!-- 营养成分预览 -->
        <NCard v-if="nutritionPreview" title="营养成分预览" size="small" :bordered="false" class="form-card preview-card">
          <div class="nutrition-preview">
            <div class="preview-item">
              <span class="preview-label">热量</span>
              <span class="preview-value">{{ nutritionPreview.calories }} 千卡</span>
            </div>
            <div class="preview-item">
              <span class="preview-label">蛋白质</span>
              <span class="preview-value">{{ nutritionPreview.protein }} 克</span>
            </div>
            <div class="preview-item">
              <span class="preview-label">碳水化合物</span>
              <span class="preview-value">{{ nutritionPreview.carbohydrates }} 克</span>
            </div>
            <div class="preview-item">
              <span class="preview-label">脂肪</span>
              <span class="preview-value">{{ nutritionPreview.fat }} 克</span>
            </div>
          </div>
        </NCard>

        <!-- 备注卡片 -->
        <NCard title="备注" size="small" :bordered="false" class="form-card">
          <NFormItem label="备注" path="notes">
            <NInput
              v-model:value="formData.notes"
              type="textarea"
              placeholder="添加备注（可选）"
              :rows="3"
            />
          </NFormItem>
        </NCard>
      </NForm>
    </div>

    <!-- 底部操作栏 -->
    <div class="mobile-footer">
      <NSpace justify="space-between">
        <NButton @click="$emit('cancel')" size="large" style="flex: 1;">
          取消
        </NButton>
        <NButton 
          type="primary" 
          @click="$emit('save')" 
          :loading="loading"
          size="large"
          style="flex: 1; margin-left: 12px;"
        >
          保存修改
        </NButton>
      </NSpace>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  NButton,
  NIcon,
  NCard,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NSelect,
  NRadioGroup,
  NRadio,
  NDatePicker,
  NTimePicker,
  NSpace,
  type FormInst,
  type FormRules
} from 'naive-ui'
import {
  ArrowBackOutline,
  RestaurantOutline,
  CreateOutline
} from '@vicons/ionicons5'

interface Props {
  formData: any
  rules: FormRules
  loading?: boolean
  foodOptions: Array<{ label: string; value: number }>
  unitOptions: Array<{ label: string; value: string }>
  nutritionPreview?: {
    calories: number
    protein: number
    carbohydrates: number
    fat: number
  } | null
}

defineProps<Props>()

defineEmits<{
  'cancel': []
  'save': []
  'food-type-change': []
  'food-change': []
}>()

const formRef = ref<FormInst | null>(null)
</script>

<style scoped>
.mobile-edit-record {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.mobile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.mobile-form-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  padding-bottom: 80px;
}

.form-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.preview-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.radio-item {
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
}

.radio-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.radio-icon {
  font-size: 18px;
}

.custom-nutrition {
  margin-top: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.nutrition-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.nutrition-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.nutrition-label {
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

.nutrition-input {
  width: 100%;
}

.quantity-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
}

.nutrition-preview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.preview-label {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 4px;
}

.preview-value {
  font-size: 16px;
  font-weight: 600;
}

.mobile-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background-color: white;
  border-top: 1px solid #e0e0e0;
  z-index: 100;
}

/* 表单项样式优化 */
:deep(.n-form-item) {
  margin-bottom: 16px;
}

:deep(.n-form-item-label) {
  font-weight: 500;
  color: #333;
}

:deep(.n-input) {
  border-radius: 8px;
}

:deep(.n-input-number) {
  border-radius: 8px;
}

:deep(.n-card__content) {
  padding: 16px;
}

:deep(.n-card-header) {
  padding: 16px 16px 0 16px;
}

:deep(.n-card-header__main) {
  font-weight: 600;
  color: #333;
}

:deep(.preview-card .n-card-header__main) {
  color: white;
}

:deep(.n-radio) {
  width: 100%;
}

:deep(.n-radio__label) {
  width: 100%;
}
</style>
