<template>
  <div class="diet-type-form">
    <NForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <NFormItem label="饮食类型名称" path="name">
        <NInput
          v-model:value="formData.name"
          placeholder="请输入饮食类型名称"
          maxlength="100"
          show-count
        />
      </NFormItem>

      <NFormItem label="英文名称" path="nameEn">
        <NInput
          v-model:value="formData.nameEn"
          placeholder="请输入英文名称（可选）"
          maxlength="100"
          show-count
        />
      </NFormItem>

      <NFormItem label="主题颜色" path="color">
        <NColorPicker
          v-model:value="formData.color"
          :modes="['hex']"
          :show-alpha="false"
        />
      </NFormItem>

      <NFormItem label="饮食描述" path="description">
        <NInput
          v-model:value="formData.description"
          type="textarea"
          placeholder="请输入饮食类型的详细描述"
          :rows="3"
          maxlength="500"
          show-count
        />
      </NFormItem>

      <NFormItem label="营养特点" path="nutritionFeatures">
        <NInput
          v-model:value="formData.nutritionFeatures"
          type="textarea"
          placeholder="请输入营养特点"
          :rows="2"
          maxlength="300"
          show-count
        />
      </NFormItem>

      <NFormItem label="主要益处" path="benefits">
        <NInput
          v-model:value="formData.benefits"
          type="textarea"
          placeholder="请输入主要益处"
          :rows="2"
          maxlength="300"
          show-count
        />
      </NFormItem>

      <NFormItem label="适合人群" path="suitablePeople">
        <NInput
          v-model:value="formData.suitablePeople"
          type="textarea"
          placeholder="请输入适合人群"
          :rows="2"
          maxlength="300"
          show-count
        />
      </NFormItem>

      <NFormItem label="图片链接" path="imageUrl">
        <NInput
          v-model:value="formData.imageUrl"
          placeholder="请输入图片链接（可选）"
          maxlength="255"
        />
      </NFormItem>
    </NForm>

    <div class="form-actions">
      <NSpace>
        <NButton @click="handleCancel">取消</NButton>
        <NButton
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ isEditing ? '更新' : '添加' }}
        </NButton>
      </NSpace>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import {
  NForm,
  NFormItem,
  NInput,
  NColorPicker,
  NButton,
  NSpace,
  type FormInst,
  type FormRules
} from 'naive-ui'
import type { DietType } from '../stores/dietType'

interface Props {
  dietType?: DietType | null
}

interface Emits {
  (e: 'save', data: Omit<DietType, 'id' | 'createdAt' | 'updatedAt'>): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInst | null>(null)
const loading = ref(false)

const isEditing = computed(() => !!props.dietType?.id)

// 表单数据
const formData = reactive({
  name: '',
  nameEn: '',
  description: '',
  nutritionFeatures: '',
  benefits: '',
  suitablePeople: '',
  imageUrl: '',
  color: '#1890ff',
  isSystem: false,
  userId: 1 // 假设当前用户ID为1
})

// 表单验证规则
const rules: FormRules = {
  name: [
    {
      required: true,
      message: '请输入饮食类型名称',
      trigger: ['input', 'blur']
    },
    {
      min: 2,
      max: 100,
      message: '名称长度应在2-100个字符之间',
      trigger: ['input', 'blur']
    }
  ],
  nameEn: [
    {
      max: 100,
      message: '英文名称长度不能超过100个字符',
      trigger: ['input', 'blur']
    }
  ],
  description: [
    {
      required: true,
      message: '请输入饮食描述',
      trigger: ['input', 'blur']
    },
    {
      max: 500,
      message: '描述长度不能超过500个字符',
      trigger: ['input', 'blur']
    }
  ],
  nutritionFeatures: [
    {
      required: true,
      message: '请输入营养特点',
      trigger: ['input', 'blur']
    },
    {
      max: 300,
      message: '营养特点长度不能超过300个字符',
      trigger: ['input', 'blur']
    }
  ],
  benefits: [
    {
      required: true,
      message: '请输入主要益处',
      trigger: ['input', 'blur']
    },
    {
      max: 300,
      message: '主要益处长度不能超过300个字符',
      trigger: ['input', 'blur']
    }
  ],
  suitablePeople: [
    {
      required: true,
      message: '请输入适合人群',
      trigger: ['input', 'blur']
    },
    {
      max: 300,
      message: '适合人群长度不能超过300个字符',
      trigger: ['input', 'blur']
    }
  ],
  imageUrl: [
    {
      max: 255,
      message: '图片链接长度不能超过255个字符',
      trigger: ['input', 'blur']
    }
  ],
  color: [
    {
      required: true,
      message: '请选择主题颜色',
      trigger: ['change', 'blur']
    }
  ]
}

// 监听props变化，更新表单数据
watch(
  () => props.dietType,
  (newDietType) => {
    if (newDietType) {
      Object.assign(formData, {
        name: newDietType.name || '',
        nameEn: newDietType.nameEn || '',
        description: newDietType.description || '',
        nutritionFeatures: newDietType.nutritionFeatures || '',
        benefits: newDietType.benefits || '',
        suitablePeople: newDietType.suitablePeople || '',
        imageUrl: newDietType.imageUrl || '',
        color: newDietType.color || '#1890ff',
        isSystem: newDietType.isSystem || false,
        userId: newDietType.userId || 1
      })
    } else {
      // 重置表单
      Object.assign(formData, {
        name: '',
        nameEn: '',
        description: '',
        nutritionFeatures: '',
        benefits: '',
        suitablePeople: '',
        imageUrl: '',
        color: '#1890ff',
        isSystem: false,
        userId: 1
      })
    }
  },
  { immediate: true }
)

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true
    
    const submitData = {
      name: formData.name,
      nameEn: formData.nameEn || undefined,
      description: formData.description,
      nutritionFeatures: formData.nutritionFeatures,
      benefits: formData.benefits,
      suitablePeople: formData.suitablePeople,
      imageUrl: formData.imageUrl || undefined,
      color: formData.color,
      isSystem: formData.isSystem,
      userId: formData.userId
    }
    
    emit('save', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.diet-type-form {
  width: 100%;
  max-width: 600px;
}

.form-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

:deep(.n-form-item-label) {
  font-weight: 500;
}

:deep(.n-input) {
  border-radius: 6px;
}

:deep(.n-color-picker) {
  width: 120px;
}
</style>
