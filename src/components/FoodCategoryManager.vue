<template>
  <div class="food-category-manager">
    <div class="manager-header">
      <div class="header-info">
        <h3>{{ dietType.name }} - 食物分类管理</h3>
        <p class="header-description">管理该饮食类型包含的食物分类</p>
      </div>
      <div class="header-actions">
        <NButton type="primary" @click="showAddModal = true">
          <template #icon>
            <NIcon><AddOutline /></NIcon>
          </template>
          添加分类
        </NButton>
      </div>
    </div>

    <div class="categories-list">
      <div
        v-for="category in sortedCategories"
        :key="category.id"
        class="category-item"
      >
        <div class="category-content">
          <div class="category-header">
            <div class="category-info">
              <h4>{{ category.categoryName }}</h4>
              <span v-if="category.categoryNameEn" class="category-name-en">
                {{ category.categoryNameEn }}
              </span>
            </div>
            <div class="category-actions">
              <NButton
                quaternary
                circle
                size="small"
                @click="editCategory(category)"
              >
                <template #icon>
                  <NIcon><CreateOutline /></NIcon>
                </template>
              </NButton>
              <NButton
                quaternary
                circle
                size="small"
                type="error"
                @click="deleteCategory(category)"
              >
                <template #icon>
                  <NIcon><TrashOutline /></NIcon>
                </template>
              </NButton>
            </div>
          </div>

          <p v-if="category.description" class="category-description">
            {{ category.description }}
          </p>

          <div class="category-details">
            <div class="detail-item">
              <span class="detail-label">食物示例：</span>
              <span class="detail-value">{{ category.examples }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">推荐摄入量：</span>
              <span class="detail-value">{{ category.recommendedAmount }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">排序：</span>
              <span class="detail-value">{{ category.sortOrder }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="categories.length === 0" class="empty-state">
      <NEmpty description="暂无食物分类">
        <template #extra>
          <NButton type="primary" @click="showAddModal = true">
            添加第一个分类
          </NButton>
        </template>
      </NEmpty>
    </div>

    <!-- 添加/编辑分类模态框 -->
    <NModal 
      v-model:show="showAddModal" 
      preset="card" 
      style="width: 600px;" 
      :title="editingCategory ? '编辑食物分类' : '添加食物分类'"
    >
      <FoodCategoryForm
        :diet-type="dietType"
        :category="editingCategory"
        @save="handleSaveCategory"
        @cancel="handleCancelEdit"
      />
    </NModal>

    <!-- 加载状态 -->
    <NSpin :show="loading" style="width: 100%;">
      <div style="height: 200px;"></div>
    </NSpin>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  NButton,
  NIcon,
  NModal,
  NEmpty,
  NSpin,
  useDialog,
  useMessage
} from 'naive-ui'
import {
  AddOutline,
  CreateOutline,
  TrashOutline
} from '@vicons/ionicons5'
import { useDietTypeStore, type DietType, type DietTypeFoodCategory } from '../stores/dietType'
import FoodCategoryForm from './FoodCategoryForm.vue'

interface Props {
  dietType: DietType
  categories: DietTypeFoodCategory[]
}

const props = defineProps<Props>()

const dietTypeStore = useDietTypeStore()
const dialog = useDialog()
const message = useMessage()

// 响应式状态
const showAddModal = ref(false)
const editingCategory = ref<DietTypeFoodCategory | null>(null)

// 计算属性
const loading = computed(() => dietTypeStore.loading)

const sortedCategories = computed(() => {
  return [...props.categories].sort((a, b) => a.sortOrder - b.sortOrder)
})

// 方法
const editCategory = (category: DietTypeFoodCategory) => {
  editingCategory.value = { ...category }
  showAddModal.value = true
}

const deleteCategory = (category: DietTypeFoodCategory) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除食物分类"${category.categoryName}"吗？此操作不可撤销。`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await dietTypeStore.deleteFoodCategory(category.id)
        message.success('删除成功')
      } catch (error) {
        message.error(error instanceof Error ? error.message : '删除失败')
      }
    }
  })
}

const handleSaveCategory = async (categoryData: any) => {
  try {
    if (editingCategory.value?.id) {
      // 编辑模式
      await dietTypeStore.updateFoodCategory(editingCategory.value.id, categoryData)
      message.success('更新成功')
    } else {
      // 添加模式
      await dietTypeStore.createFoodCategory({
        ...categoryData,
        dietTypeId: props.dietType.id
      })
      message.success('添加成功')
    }
    showAddModal.value = false
    editingCategory.value = null
  } catch (error) {
    message.error(error instanceof Error ? error.message : '保存失败')
  }
}

const handleCancelEdit = () => {
  showAddModal.value = false
  editingCategory.value = null
}
</script>

<style scoped>
.food-category-manager {
  width: 100%;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.header-info h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-description {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.categories-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.category-item {
  padding: 20px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.category-item:hover {
  border-color: #d0d0d0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.category-info h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.category-name-en {
  display: block;
  margin-top: 4px;
  font-size: 14px;
  color: #666;
  font-style: italic;
}

.category-actions {
  display: flex;
  gap: 8px;
}

.category-description {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.category-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  gap: 8px;
}

.detail-label {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  min-width: 100px;
  flex-shrink: 0;
}

.detail-value {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .manager-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .category-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .category-actions {
    align-self: flex-end;
  }
  
  .detail-item {
    flex-direction: column;
    gap: 4px;
  }
  
  .detail-label {
    min-width: auto;
  }
}
</style>
