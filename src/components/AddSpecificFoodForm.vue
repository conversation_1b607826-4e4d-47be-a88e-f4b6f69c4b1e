<template>
  <div class="add-specific-food-form">
    <div class="form-header">
      <h4>为"{{ categoryName }}"添加具体食物</h4>
      <p class="form-description">您可以添加具体的食物名称、描述和分量信息</p>
    </div>

    <NForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <NFormItem label="食物名称" path="name">
        <NInput
          v-model:value="formData.name"
          placeholder="请输入食物名称，如'橄榄油拌菠菜'"
          maxlength="100"
          show-count
        />
      </NFormItem>

      <NFormItem label="食物描述" path="description">
        <NInput
          v-model:value="formData.description"
          placeholder="请输入食物描述（可选），如'新鲜菠菜配橄榄油和柠檬汁'"
          maxlength="200"
          show-count
        />
      </NFormItem>

      <NGrid :cols="2" :x-gap="12">
        <NGridItem>
          <NFormItem label="分量" path="amount">
            <NInputNumber
              v-model:value="formData.amount"
              placeholder="分量"
              :min="0"
              :precision="1"
              style="width: 100%;"
            />
          </NFormItem>
        </NGridItem>
        <NGridItem>
          <NFormItem label="单位" path="unit">
            <NSelect
              v-model:value="formData.unit"
              placeholder="选择单位"
              :options="unitOptions"
              clearable
            />
          </NFormItem>
        </NGridItem>
      </NGrid>

      <!-- 快速添加建议 -->
      <div v-if="suggestions.length > 0" class="suggestions-section">
        <NFormItem label="快速选择">
          <div class="suggestions-container">
            <p class="suggestions-label">常见{{ categoryName }}：</p>
            <div class="suggestions-list">
              <NButton
                v-for="suggestion in suggestions"
                :key="suggestion.name"
                size="small"
                quaternary
                type="info"
                @click="applySuggestion(suggestion)"
              >
                {{ suggestion.name }}
                <span v-if="suggestion.amount && suggestion.unit" class="suggestion-amount">
                  ({{ suggestion.amount }}{{ suggestion.unit }})
                </span>
              </NButton>
            </div>
          </div>
        </NFormItem>
      </div>
    </NForm>

    <div class="form-actions">
      <NSpace>
        <NButton @click="handleCancel">取消</NButton>
        <NButton
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          添加食物
        </NButton>
      </NSpace>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import {
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NSelect,
  NButton,
  NSpace,
  NGrid,
  NGridItem,
  type FormInst,
  type FormRules,
  type SelectOption
} from 'naive-ui'

interface Props {
  categoryName: string
}

interface Emits {
  (e: 'save', data: {
    name: string
    description?: string
    amount?: number
    unit?: string
  }): void
  (e: 'cancel'): void
}

interface FoodSuggestion {
  name: string
  description?: string
  amount?: number
  unit?: string
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInst | null>(null)
const loading = ref(false)

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  amount: undefined as number | undefined,
  unit: 'g'
})

// 单位选项
const unitOptions: SelectOption[] = [
  { label: '克 (g)', value: 'g' },
  { label: '千克 (kg)', value: 'kg' },
  { label: '毫升 (ml)', value: 'ml' },
  { label: '升 (L)', value: 'L' },
  { label: '个', value: '个' },
  { label: '片', value: '片' },
  { label: '块', value: '块' },
  { label: '根', value: '根' },
  { label: '颗', value: '颗' },
  { label: '杯', value: '杯' },
  { label: '汤匙', value: '汤匙' },
  { label: '茶匙', value: '茶匙' }
]

// 根据分类名称提供建议
const suggestions = computed<FoodSuggestion[]>(() => {
  const categoryName = props.categoryName.toLowerCase()
  
  if (categoryName.includes('蔬菜')) {
    return [
      { name: '橄榄油拌菠菜', description: '新鲜菠菜配橄榄油', amount: 200, unit: 'g' },
      { name: '烤茄子', description: '烤制的茄子片', amount: 150, unit: 'g' },
      { name: '凉拌黄瓜', amount: 100, unit: 'g' },
      { name: '西红柿沙拉', amount: 150, unit: 'g' },
      { name: '胡萝卜丝', amount: 100, unit: 'g' }
    ]
  } else if (categoryName.includes('水果')) {
    return [
      { name: '新鲜橙子', amount: 1, unit: '个' },
      { name: '苹果', amount: 1, unit: '个' },
      { name: '葡萄', amount: 100, unit: 'g' },
      { name: '香蕉', amount: 1, unit: '根' },
      { name: '蓝莓', amount: 50, unit: 'g' }
    ]
  } else if (categoryName.includes('坚果')) {
    return [
      { name: '混合坚果', description: '核桃、杏仁、榛子混合', amount: 30, unit: 'g' },
      { name: '核桃', amount: 20, unit: 'g' },
      { name: '杏仁', amount: 25, unit: 'g' },
      { name: '腰果', amount: 20, unit: 'g' }
    ]
  } else if (categoryName.includes('全谷物') || categoryName.includes('谷物')) {
    return [
      { name: '全麦面包', amount: 2, unit: '片' },
      { name: '糙米饭', amount: 150, unit: 'g' },
      { name: '燕麦粥', amount: 1, unit: '碗' },
      { name: '全麦意面', amount: 100, unit: 'g' }
    ]
  } else if (categoryName.includes('豆类')) {
    return [
      { name: '煮扁豆', amount: 100, unit: 'g' },
      { name: '鹰嘴豆沙拉', amount: 80, unit: 'g' },
      { name: '红豆汤', amount: 1, unit: '碗' },
      { name: '豆腐', amount: 100, unit: 'g' }
    ]
  } else if (categoryName.includes('鱼类')) {
    return [
      { name: '烤三文鱼', description: '香草烤三文鱼', amount: 150, unit: 'g' },
      { name: '清蒸鲈鱼', amount: 200, unit: 'g' },
      { name: '金枪鱼沙拉', amount: 100, unit: 'g' },
      { name: '沙丁鱼', amount: 80, unit: 'g' }
    ]
  } else if (categoryName.includes('橄榄油')) {
    return [
      { name: '特级初榨橄榄油', amount: 2, unit: '汤匙' },
      { name: '橄榄油调味汁', amount: 1, unit: '汤匙' }
    ]
  }
  
  return []
})

// 表单验证规则
const rules: FormRules = {
  name: [
    {
      required: true,
      message: '请输入食物名称',
      trigger: ['input', 'blur']
    },
    {
      min: 1,
      max: 100,
      message: '食物名称长度应在1-100个字符之间',
      trigger: ['input', 'blur']
    }
  ],
  description: [
    {
      max: 200,
      message: '描述长度不能超过200个字符',
      trigger: ['input', 'blur']
    }
  ],
  amount: [
    {
      type: 'number',
      min: 0,
      message: '分量必须大于等于0',
      trigger: ['input', 'blur']
    }
  ]
}

// 方法
const applySuggestion = (suggestion: FoodSuggestion) => {
  formData.name = suggestion.name
  formData.description = suggestion.description || ''
  formData.amount = suggestion.amount
  formData.unit = suggestion.unit || 'g'
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true
    
    const submitData = {
      name: formData.name,
      description: formData.description || undefined,
      amount: formData.amount,
      unit: formData.unit || undefined
    }
    
    emit('save', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.add-specific-food-form {
  width: 100%;
  max-width: 500px;
}

.form-header {
  margin-bottom: 20px;
  text-align: center;
}

.form-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.form-description {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.suggestions-section {
  margin-top: 16px;
}

.suggestions-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suggestions-label {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.suggestions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-amount {
  margin-left: 4px;
  font-size: 12px;
  color: #999;
}

.form-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

:deep(.n-form-item-label) {
  font-weight: 500;
}

:deep(.n-input) {
  border-radius: 6px;
}

:deep(.n-input-number) {
  border-radius: 6px;
}

:deep(.n-select) {
  border-radius: 6px;
}

:deep(.n-button--quaternary) {
  border: 1px solid #e0e0e0;
}

:deep(.n-button--quaternary:hover) {
  border-color: #1890ff;
  background-color: #f0f8ff;
}
</style>
