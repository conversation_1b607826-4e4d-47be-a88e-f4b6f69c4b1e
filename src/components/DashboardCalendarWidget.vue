<template>
  <NCard title="饮食计划日历" class="calendar-widget">
    <template #header-extra>
      <NButton text @click="goToFullCalendar">
        <template #icon>
          <NIcon><CalendarOutline /></NIcon>
        </template>
        查看完整日历
      </NButton>
    </template>

    <div class="widget-content">
      <!-- 迷你统计 -->
      <div class="mini-stats">
        <div class="stat-item">
          <div class="stat-value">{{ todayCompletionRate }}%</div>
          <div class="stat-label">今日完成度</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ weeklyAverage }}%</div>
          <div class="stat-label">本周平均</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ currentStreak }}</div>
          <div class="stat-label">连续天数</div>
        </div>
      </div>

      <!-- 迷你日历 -->
      <div class="mini-calendar">
        <VCalendar
          v-model="selectedDate"
          :attributes="calendarAttributes"
          :locale="locale"
          :first-day-of-week="2"
          is-expanded
          borderless
          @dayclick="handleDayClick"
        />
      </div>

      <!-- 今日计划快览 -->
      <div v-if="todayPlan" class="today-plan">
        <h4>今日计划</h4>
        <div class="plan-summary">
          <div class="plan-header">
            <span class="plan-name">{{ todayPlan.planName || '饮食计划' }}</span>
            <NTag :color="getDietTypeColor(todayPlan.dietTypeId)" size="small">
              {{ getDietTypeName(todayPlan.dietTypeId) }}
            </NTag>
          </div>
          <div class="plan-progress">
            <NProgress
              :percentage="todayCompletionRate"
              :show-indicator="false"
              :height="4"
            />
            <span class="progress-text">
              {{ getCompletedItemsCount(todayPlan.id) }} / {{ getTotalItemsCount(todayPlan.id) }} 项已完成
            </span>
          </div>
        </div>
      </div>

      <div v-else class="no-plan">
        <NEmpty size="small" description="今天还没有饮食计划">
          <template #extra>
            <NButton size="small" type="primary" @click="createTodayPlan">
              创建今日计划
            </NButton>
          </template>
        </NEmpty>
      </div>
    </div>
  </NCard>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Calendar as VCalendar } from 'v-calendar'
import {
  NCard,
  NButton,
  NIcon,
  NTag,
  NProgress,
  NEmpty
} from 'naive-ui'
import { CalendarOutline } from '@vicons/ionicons5'
import { useRouter } from 'vue-router'
import { useDailyDietPlanStore } from '../stores/dailyDietPlan'
import { useDietTypeStore } from '../stores/dietType'

const router = useRouter()
const dailyPlanStore = useDailyDietPlanStore()
const dietTypeStore = useDietTypeStore()

// 响应式状态
const selectedDate = ref(new Date())

// 本地化配置
const locale = ref({
  id: 'zh-CN',
  firstDayOfWeek: 2,
  masks: {
    weekdays: 'WWW',
    navMonths: 'MMMM',
    title: 'MMMM YYYY'
  }
})

// 计算属性
const todayString = computed(() => {
  return new Date().toISOString().split('T')[0]
})

const todayPlan = computed(() => {
  return dailyPlanStore.getPlanByDate(todayString.value)
})

const todayCompletionRate = computed(() => {
  if (!todayPlan.value) return 0
  return getCompletionRate(todayPlan.value.id)
})

const weeklyAverage = computed(() => {
  const today = new Date()
  const weekPlans = []
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() - i)
    const dateString = date.toISOString().split('T')[0]
    const plan = dailyPlanStore.getPlanByDate(dateString)
    if (plan) {
      weekPlans.push(getCompletionRate(plan.id))
    }
  }
  
  if (weekPlans.length === 0) return 0
  return Math.round(weekPlans.reduce((sum, rate) => sum + rate, 0) / weekPlans.length)
})

const currentStreak = computed(() => {
  const sortedPlans = [...dailyPlanStore.dailyPlans]
    .sort((a, b) => new Date(b.planDate).getTime() - new Date(a.planDate).getTime())
  
  let streak = 0
  for (const plan of sortedPlans) {
    if (getCompletionRate(plan.id) === 100) {
      streak++
    } else {
      break
    }
  }
  return streak
})

// 日历属性 - 只显示最近的计划
const calendarAttributes = computed(() => {
  const attributes: any[] = []
  
  // 只显示当前月的计划
  const currentMonth = new Date().getMonth()
  const currentYear = new Date().getFullYear()
  
  dailyPlanStore.dailyPlans.forEach(plan => {
    const planDate = new Date(plan.planDate)
    if (planDate.getMonth() === currentMonth && planDate.getFullYear() === currentYear) {
      const completionRate = getCompletionRate(plan.id)
      
      let dotColor = '#d9d9d9'
      if (completionRate === 100) {
        dotColor = '#52c41a'
      } else if (completionRate > 0) {
        dotColor = '#faad14'
      } else {
        dotColor = '#1890ff'
      }

      attributes.push({
        key: `plan-${plan.id}`,
        dates: planDate,
        dot: {
          color: dotColor,
          class: 'mini-calendar-dot'
        }
      })
    }
  })
  
  return attributes
})

// 方法
const getCompletionRate = (planId: number): number => {
  const items = dailyPlanStore.getPlanItemsByPlanId(planId)
  if (items.length === 0) return 0
  
  const completedItems = items.filter(item => item.isConsumed).length
  return Math.round((completedItems / items.length) * 100)
}

const getCompletedItemsCount = (planId: number): number => {
  const items = dailyPlanStore.getPlanItemsByPlanId(planId)
  return items.filter(item => item.isConsumed).length
}

const getTotalItemsCount = (planId: number): number => {
  return dailyPlanStore.getPlanItemsByPlanId(planId).length
}

const getDietTypeName = (dietTypeId?: number): string => {
  if (!dietTypeId) return '自定义计划'
  const dietType = dietTypeStore.dietTypes.find(dt => dt.id === dietTypeId)
  return dietType?.name || '未知类型'
}

const getDietTypeColor = (dietTypeId?: number): string => {
  if (!dietTypeId) return '#d9d9d9'
  const dietType = dietTypeStore.dietTypes.find(dt => dt.id === dietTypeId)
  return dietType?.color || '#1890ff'
}

const handleDayClick = (day: any) => {
  const dateString = day.date.toISOString().split('T')[0]
  router.push({
    name: 'DietPlanCalendar',
    query: { date: dateString }
  })
}

const goToFullCalendar = () => {
  router.push({ name: 'DietPlanCalendar' })
}

const createTodayPlan = () => {
  router.push({
    name: 'DailyDietPlan',
    query: { date: todayString.value }
  })
}

// 生命周期
onMounted(async () => {
  await dietTypeStore.fetchDietTypes()
  await dailyPlanStore.fetchDailyPlans(1)
  
  // 获取今日计划的详细项目
  if (todayPlan.value) {
    await dailyPlanStore.fetchPlanItems(todayPlan.value.id)
  }
})
</script>

<style scoped>
.calendar-widget {
  height: 100%;
}

.widget-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.mini-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 6px;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
}

.mini-calendar {
  /* V-Calendar 会自动调整大小 */
}

.today-plan h4 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 14px;
}

.plan-summary {
  padding: 12px;
  background-color: #f6ffed;
  border-radius: 6px;
  border: 1px solid #b7eb8f;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.plan-name {
  font-weight: 500;
  color: #262626;
}

.plan-progress {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  text-align: right;
}

.no-plan {
  text-align: center;
  padding: 20px;
}

/* V-Calendar 迷你样式 */
:deep(.vc-container) {
  border: none;
}

:deep(.vc-header) {
  padding: 8px 12px;
}

:deep(.vc-weeks) {
  padding: 0 12px 12px;
}

:deep(.vc-day) {
  min-height: 32px;
}

:deep(.vc-day-content) {
  font-size: 13px;
}

:deep(.mini-calendar-dot) {
  width: 4px;
  height: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mini-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }
  
  .stat-item {
    padding: 8px;
  }
  
  .stat-value {
    font-size: 16px;
  }
  
  .stat-label {
    font-size: 11px;
  }
}
</style>
