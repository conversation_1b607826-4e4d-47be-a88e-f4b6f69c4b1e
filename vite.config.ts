import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // 自动导入 Vue 相关函数
    AutoImport({
      imports: [
        'vue',
        'vue-router',
        'pinia',
        {
          'naive-ui': [
            'useDialog',
            'useMessage',
            'useNotification',
            'useLoadingBar'
          ]
        }
      ],
      dts: true, // 生成类型声明文件
      eslintrc: {
        enabled: true, // 生成 eslint 配置
      },
    }),
    // 自动导入组件
    Components({
      resolvers: [NaiveUiResolver()],
      dts: true, // 生成类型声明文件
    }),
  ],

  // 路径别名
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },

  // 开发服务器配置
  server: {
    port: 7002,
    host: '0.0.0.0', // 允许外部访问，支持移动端调试
    open: true, // 自动打开浏览器
    cors: true, // 允许跨域
    allowedHosts: ['wx.fyg.cn'],
    proxy: {
      // 代理 API 请求
      '/api': {
        target: 'http://localhost:3000', // 后端服务地址
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },

  // 构建配置
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,

    // 代码分割配置
    rollupOptions: {
      output: {
        // 手动分块
        manualChunks: {
          // Vue 相关
          vue: ['vue', 'vue-router', 'pinia'],
          // UI 组件库
          'naive-ui': ['naive-ui'],
          // 图标库
          icons: ['@vicons/ionicons5'],
          // 工具库
          utils: ['dayjs', 'axios'],
        },
        // 文件命名
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const fileName = assetInfo.names?.[0] || assetInfo.originalFileNames?.[0] || 'asset'
          const info = fileName.split('.')
          let extType = info[info.length - 1]
          if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(fileName)) {
            extType = 'media'
          } else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(fileName)) {
            extType = 'img'
          } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(fileName)) {
            extType = 'fonts'
          }
          return `${extType}/[name]-[hash].[ext]`
        },
      },
    },

    // 压缩配置
    minify: 'terser',

    // 分包大小警告阈值
    chunkSizeWarningLimit: 1000,
  },

  // 预览服务器配置
  preview: {
    port: 7003,
    host: '0.0.0.0',
    open: true,
  },
})
