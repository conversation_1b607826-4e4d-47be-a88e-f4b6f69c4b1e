-- 添加NRV%字段的数据库迁移脚本
-- 执行日期: 2025-08-25

-- 1. 为foods表添加NRV%字段
ALTER TABLE foods 
ADD COLUMN calories_nrv DECIMAL(5,2) DEFAULT 0 COMMENT '能量NRV% (基于2000千卡)' AFTER sugar,
ADD COLUMN carbohydrates_nrv DECIMAL(5,2) DEFAULT 0 COMMENT '碳水化合物NRV% (基于300g)' AFTER calories_nrv,
ADD COLUMN total_fat_nrv DECIMAL(5,2) DEFAULT 0 COMMENT '脂肪NRV% (基于60g)' AFTER carbohydrates_nrv,
ADD COLUMN protein_nrv DECIMAL(5,2) DEFAULT 0 COMMENT '蛋白质NRV% (基于60g)' AFTER total_fat_nrv,
ADD COLUMN saturated_fat_nrv DECIMAL(5,2) DEFAULT 0 COMMENT '饱和脂肪NRV% (基于20g)' AFTER protein_nrv,
ADD COLUMN cholesterol_nrv DECIMAL(5,2) DEFAULT 0 COMMENT '胆固醇NRV% (基于300mg)' AFTER saturated_fat_nrv,
ADD COLUMN sodium_nrv DECIMAL(5,2) DEFAULT 0 COMMENT '钠NRV% (基于2000mg)' AFTER cholesterol_nrv,
ADD COLUMN potassium_nrv DECIMAL(5,2) DEFAULT 0 COMMENT '钾NRV% (基于3500mg)' AFTER sodium_nrv,
ADD COLUMN dietary_fiber_nrv DECIMAL(5,2) DEFAULT 0 COMMENT '膳食纤维NRV% (基于25g)' AFTER potassium_nrv,
ADD COLUMN vitamin_a_nrv DECIMAL(5,2) DEFAULT 0 COMMENT '维生素A NRV% (基于800μg)' AFTER dietary_fiber_nrv,
ADD COLUMN vitamin_c_nrv DECIMAL(5,2) DEFAULT 0 COMMENT '维生素C NRV% (基于100mg)' AFTER vitamin_a_nrv,
ADD COLUMN calcium_nrv DECIMAL(5,2) DEFAULT 0 COMMENT '钙 NRV% (基于800mg)' AFTER vitamin_c_nrv,
ADD COLUMN iron_nrv DECIMAL(5,2) DEFAULT 0 COMMENT '铁 NRV% (基于15mg)' AFTER calcium_nrv;

-- 2. 为daily_nutrition_records表添加实际摄入NRV%字段
ALTER TABLE daily_nutrition_records 
ADD COLUMN actual_calories_nrv DECIMAL(8,2) DEFAULT 0 AFTER actual_iron,
ADD COLUMN actual_carbohydrates_nrv DECIMAL(8,2) DEFAULT 0 AFTER actual_calories_nrv,
ADD COLUMN actual_total_fat_nrv DECIMAL(8,2) DEFAULT 0 AFTER actual_carbohydrates_nrv,
ADD COLUMN actual_protein_nrv DECIMAL(8,2) DEFAULT 0 AFTER actual_total_fat_nrv,
ADD COLUMN actual_saturated_fat_nrv DECIMAL(8,2) DEFAULT 0 AFTER actual_protein_nrv,
ADD COLUMN actual_cholesterol_nrv DECIMAL(8,2) DEFAULT 0 AFTER actual_saturated_fat_nrv,
ADD COLUMN actual_sodium_nrv DECIMAL(8,2) DEFAULT 0 AFTER actual_cholesterol_nrv,
ADD COLUMN actual_potassium_nrv DECIMAL(8,2) DEFAULT 0 AFTER actual_sodium_nrv,
ADD COLUMN actual_dietary_fiber_nrv DECIMAL(8,2) DEFAULT 0 AFTER actual_potassium_nrv,
ADD COLUMN actual_vitamin_a_nrv DECIMAL(8,2) DEFAULT 0 AFTER actual_dietary_fiber_nrv,
ADD COLUMN actual_vitamin_c_nrv DECIMAL(8,2) DEFAULT 0 AFTER actual_vitamin_a_nrv,
ADD COLUMN actual_calcium_nrv DECIMAL(8,2) DEFAULT 0 AFTER actual_vitamin_c_nrv,
ADD COLUMN actual_iron_nrv DECIMAL(8,2) DEFAULT 0 AFTER actual_calcium_nrv;

-- 3. 为daily_nutrition_summary表添加总NRV%字段
ALTER TABLE daily_nutrition_summary 
ADD COLUMN total_calories_nrv DECIMAL(10,2) DEFAULT 0 AFTER total_iron,
ADD COLUMN total_carbohydrates_nrv DECIMAL(10,2) DEFAULT 0 AFTER total_calories_nrv,
ADD COLUMN total_fat_nrv DECIMAL(10,2) DEFAULT 0 AFTER total_carbohydrates_nrv,
ADD COLUMN total_protein_nrv DECIMAL(10,2) DEFAULT 0 AFTER total_fat_nrv,
ADD COLUMN total_saturated_fat_nrv DECIMAL(10,2) DEFAULT 0 AFTER total_protein_nrv,
ADD COLUMN total_cholesterol_nrv DECIMAL(10,2) DEFAULT 0 AFTER total_saturated_fat_nrv,
ADD COLUMN total_sodium_nrv DECIMAL(10,2) DEFAULT 0 AFTER total_cholesterol_nrv,
ADD COLUMN total_potassium_nrv DECIMAL(10,2) DEFAULT 0 AFTER total_sodium_nrv,
ADD COLUMN total_dietary_fiber_nrv DECIMAL(10,2) DEFAULT 0 AFTER total_potassium_nrv,
ADD COLUMN total_vitamin_a_nrv DECIMAL(10,2) DEFAULT 0 AFTER total_dietary_fiber_nrv,
ADD COLUMN total_vitamin_c_nrv DECIMAL(10,2) DEFAULT 0 AFTER total_vitamin_a_nrv,
ADD COLUMN total_calcium_nrv DECIMAL(10,2) DEFAULT 0 AFTER total_vitamin_c_nrv,
ADD COLUMN total_iron_nrv DECIMAL(10,2) DEFAULT 0 AFTER total_calcium_nrv;

-- 4. 删除旧的触发器
DROP TRIGGER IF EXISTS update_daily_summary_after_insert;
DROP TRIGGER IF EXISTS update_daily_summary_after_delete;

-- 5. 重新创建包含NRV%字段的触发器
DELIMITER //

CREATE TRIGGER update_daily_summary_after_insert
AFTER INSERT ON daily_nutrition_records
FOR EACH ROW
BEGIN
    INSERT INTO daily_nutrition_summary (
        user_id, summary_date,
        total_calories, total_carbohydrates, total_fat, total_protein,
        total_saturated_fat, total_trans_fat, total_cholesterol,
        total_sodium, total_potassium, total_dietary_fiber, total_sugar,
        total_vitamin_a, total_vitamin_c, total_calcium, total_iron,
        total_calories_nrv, total_carbohydrates_nrv, total_fat_nrv, total_protein_nrv,
        total_saturated_fat_nrv, total_cholesterol_nrv, total_sodium_nrv,
        total_potassium_nrv, total_dietary_fiber_nrv, total_vitamin_a_nrv,
        total_vitamin_c_nrv, total_calcium_nrv, total_iron_nrv
    ) VALUES (
        NEW.user_id, NEW.record_date,
        NEW.actual_calories, NEW.actual_carbohydrates, NEW.actual_total_fat, NEW.actual_protein,
        NEW.actual_saturated_fat, NEW.actual_trans_fat, NEW.actual_cholesterol,
        NEW.actual_sodium, NEW.actual_potassium, NEW.actual_dietary_fiber, NEW.actual_sugar,
        NEW.actual_vitamin_a, NEW.actual_vitamin_c, NEW.actual_calcium, NEW.actual_iron,
        NEW.actual_calories_nrv, NEW.actual_carbohydrates_nrv, NEW.actual_total_fat_nrv, NEW.actual_protein_nrv,
        NEW.actual_saturated_fat_nrv, NEW.actual_cholesterol_nrv, NEW.actual_sodium_nrv,
        NEW.actual_potassium_nrv, NEW.actual_dietary_fiber_nrv, NEW.actual_vitamin_a_nrv,
        NEW.actual_vitamin_c_nrv, NEW.actual_calcium_nrv, NEW.actual_iron_nrv
    ) ON DUPLICATE KEY UPDATE
        total_calories = total_calories + NEW.actual_calories,
        total_carbohydrates = total_carbohydrates + NEW.actual_carbohydrates,
        total_fat = total_fat + NEW.actual_total_fat,
        total_protein = total_protein + NEW.actual_protein,
        total_saturated_fat = total_saturated_fat + NEW.actual_saturated_fat,
        total_trans_fat = total_trans_fat + NEW.actual_trans_fat,
        total_cholesterol = total_cholesterol + NEW.actual_cholesterol,
        total_sodium = total_sodium + NEW.actual_sodium,
        total_potassium = total_potassium + NEW.actual_potassium,
        total_dietary_fiber = total_dietary_fiber + NEW.actual_dietary_fiber,
        total_sugar = total_sugar + NEW.actual_sugar,
        total_vitamin_a = total_vitamin_a + NEW.actual_vitamin_a,
        total_vitamin_c = total_vitamin_c + NEW.actual_vitamin_c,
        total_calcium = total_calcium + NEW.actual_calcium,
        total_iron = total_iron + NEW.actual_iron,
        total_calories_nrv = total_calories_nrv + NEW.actual_calories_nrv,
        total_carbohydrates_nrv = total_carbohydrates_nrv + NEW.actual_carbohydrates_nrv,
        total_fat_nrv = total_fat_nrv + NEW.actual_total_fat_nrv,
        total_protein_nrv = total_protein_nrv + NEW.actual_protein_nrv,
        total_saturated_fat_nrv = total_saturated_fat_nrv + NEW.actual_saturated_fat_nrv,
        total_cholesterol_nrv = total_cholesterol_nrv + NEW.actual_cholesterol_nrv,
        total_sodium_nrv = total_sodium_nrv + NEW.actual_sodium_nrv,
        total_potassium_nrv = total_potassium_nrv + NEW.actual_potassium_nrv,
        total_dietary_fiber_nrv = total_dietary_fiber_nrv + NEW.actual_dietary_fiber_nrv,
        updated_at = CURRENT_TIMESTAMP;
END//

CREATE TRIGGER update_daily_summary_after_delete
AFTER DELETE ON daily_nutrition_records
FOR EACH ROW
BEGIN
    UPDATE daily_nutrition_summary SET
        total_calories = total_calories - OLD.actual_calories,
        total_carbohydrates = total_carbohydrates - OLD.actual_carbohydrates,
        total_fat = total_fat - OLD.actual_total_fat,
        total_protein = total_protein - OLD.actual_protein,
        total_saturated_fat = total_saturated_fat - OLD.actual_saturated_fat,
        total_trans_fat = total_trans_fat - OLD.actual_trans_fat,
        total_cholesterol = total_cholesterol - OLD.actual_cholesterol,
        total_sodium = total_sodium - OLD.actual_sodium,
        total_potassium = total_potassium - OLD.actual_potassium,
        total_dietary_fiber = total_dietary_fiber - OLD.actual_dietary_fiber,
        total_sugar = total_sugar - OLD.actual_sugar,
        total_vitamin_a = total_vitamin_a - OLD.actual_vitamin_a,
        total_vitamin_c = total_vitamin_c - OLD.actual_vitamin_c,
        total_calcium = total_calcium - OLD.actual_calcium,
        total_iron = total_iron - OLD.actual_iron,
        total_calories_nrv = total_calories_nrv - OLD.actual_calories_nrv,
        total_carbohydrates_nrv = total_carbohydrates_nrv - OLD.actual_carbohydrates_nrv,
        total_fat_nrv = total_fat_nrv - OLD.actual_total_fat_nrv,
        total_protein_nrv = total_protein_nrv - OLD.actual_protein_nrv,
        total_saturated_fat_nrv = total_saturated_fat_nrv - OLD.actual_saturated_fat_nrv,
        total_cholesterol_nrv = total_cholesterol_nrv - OLD.actual_cholesterol_nrv,
        total_sodium_nrv = total_sodium_nrv - OLD.actual_sodium_nrv,
        total_potassium_nrv = total_potassium_nrv - OLD.actual_potassium_nrv,
        total_dietary_fiber_nrv = total_dietary_fiber_nrv - OLD.actual_dietary_fiber_nrv,
        updated_at = CURRENT_TIMESTAMP
    WHERE user_id = OLD.user_id AND summary_date = OLD.record_date;
END//

DELIMITER ;

-- 6. 创建NRV参考值常量表（可选，用于前端计算）
CREATE TABLE IF NOT EXISTS nrv_reference_values (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nutrient_name VARCHAR(50) NOT NULL UNIQUE,
    reference_value DECIMAL(10,2) NOT NULL,
    unit VARCHAR(10) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入NRV参考值
INSERT INTO nrv_reference_values (nutrient_name, reference_value, unit, description) VALUES
('calories', 2000, 'kcal', '成人每日能量参考摄入量'),
('carbohydrates', 300, 'g', '成人每日碳水化合物参考摄入量'),
('total_fat', 60, 'g', '成人每日脂肪参考摄入量'),
('protein', 60, 'g', '成人每日蛋白质参考摄入量'),
('saturated_fat', 20, 'g', '成人每日饱和脂肪参考摄入量'),
('cholesterol', 300, 'mg', '成人每日胆固醇参考摄入量'),
('sodium', 2000, 'mg', '成人每日钠参考摄入量'),
('potassium', 3500, 'mg', '成人每日钾参考摄入量'),
('dietary_fiber', 25, 'g', '成人每日膳食纤维参考摄入量'),
('vitamin_a', 800, 'μg', '成人每日维生素A参考摄入量'),
('vitamin_c', 100, 'mg', '成人每日维生素C参考摄入量'),
('calcium', 800, 'mg', '成人每日钙参考摄入量'),
('iron', 15, 'mg', '成人每日铁参考摄入量')
ON DUPLICATE KEY UPDATE
    reference_value = VALUES(reference_value),
    unit = VALUES(unit),
    description = VALUES(description);

-- 7. 更新现有扩展营养成分JSON结构（如果需要）
-- 注意：这个步骤需要根据实际数据情况手动执行
-- 新的JSON结构：[{"name":"", "nutrition": , "unit":"", "nrv":}]
-- 旧的JSON结构可能是：{"营养素名": 数值}

-- 示例更新语句（需要根据实际情况调整）:
-- UPDATE foods
-- SET extended_nutrition = JSON_ARRAY(
--   JSON_OBJECT('name', '维生素B1', 'nutrition', 0.1, 'unit', 'mg', 'nrv', 10.0)
-- )
-- WHERE extended_nutrition IS NOT NULL AND JSON_TYPE(extended_nutrition) = 'OBJECT';
