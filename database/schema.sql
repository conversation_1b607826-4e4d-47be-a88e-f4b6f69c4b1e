-- 营养记录应用数据库设计
-- 数据库: nutrition_tracker

-- 1. 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    nickname VARCHAR(50),
    avatar_url VARCHAR(255),
    gender ENUM('male', 'female', 'other') DEFAULT 'other',
    birth_date DATE,
    height DECIMAL(5,2), -- 身高(cm)
    weight DECIMAL(5,2), -- 体重(kg)
    activity_level ENUM('sedentary', 'light', 'moderate', 'active', 'very_active') DEFAULT 'moderate',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. 食物标签表
CREATE TABLE food_tags (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    color VARCHAR(7) DEFAULT '#1890ff', -- 标签颜色
    description TEXT,
    is_system BOOLEAN DEFAULT FALSE, -- 是否为系统默认标签
    user_id INT, -- 自定义标签的创建者，系统标签为NULL
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_tag (user_id, name),
    INDEX idx_user_id (user_id),
    INDEX idx_is_system (is_system)
);

-- 3. 食物表
CREATE TABLE foods (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    brand VARCHAR(100), -- 品牌
    barcode VARCHAR(50), -- 条形码
    user_id INT NOT NULL, -- 创建者
    is_public BOOLEAN DEFAULT FALSE, -- 是否公开给其他用户
    
    -- 常用营养成分 (每100g)
    calories DECIMAL(8,2) DEFAULT 0, -- 卡路里 (千卡)
    carbohydrates DECIMAL(8,2) DEFAULT 0, -- 总碳水化合物 (g)
    total_fat DECIMAL(8,2) DEFAULT 0, -- 总脂肪 (g)
    protein DECIMAL(8,2) DEFAULT 0, -- 蛋白质 (g)
    saturated_fat DECIMAL(8,2) DEFAULT 0, -- 饱和脂肪 (g)
    trans_fat DECIMAL(8,2) DEFAULT 0, -- 反式脂肪 (g)
    cholesterol DECIMAL(8,2) DEFAULT 0, -- 胆固醇 (mg)
    sodium DECIMAL(8,2) DEFAULT 0, -- 钠 (mg)
    potassium DECIMAL(8,2) DEFAULT 0, -- 钾 (mg)
    dietary_fiber DECIMAL(8,2) DEFAULT 0, -- 膳食纤维 (g)
    sugar DECIMAL(8,2) DEFAULT 0, -- 糖 (g)

    -- NRV% (营养素参考值百分比，每100g)
    calories_nrv DECIMAL(5,2) DEFAULT 0, -- 能量NRV% (基于2000千卡)
    carbohydrates_nrv DECIMAL(5,2) DEFAULT 0, -- 碳水化合物NRV% (基于300g)
    total_fat_nrv DECIMAL(5,2) DEFAULT 0, -- 脂肪NRV% (基于60g)
    protein_nrv DECIMAL(5,2) DEFAULT 0, -- 蛋白质NRV% (基于60g)
    saturated_fat_nrv DECIMAL(5,2) DEFAULT 0, -- 饱和脂肪NRV% (基于20g)
    cholesterol_nrv DECIMAL(5,2) DEFAULT 0, -- 胆固醇NRV% (基于300mg)
    sodium_nrv DECIMAL(5,2) DEFAULT 0, -- 钠NRV% (基于2000mg)
    potassium_nrv DECIMAL(5,2) DEFAULT 0, -- 钾NRV% (基于3500mg)
    dietary_fiber_nrv DECIMAL(5,2) DEFAULT 0, -- 膳食纤维NRV% (基于25g)
    -- 注意：sugar 和 trans_fat 没有官方NRV参考值，因此不设置对应的nrv字段

    -- 维生素 (每100g)
    vitamin_a DECIMAL(8,2) DEFAULT 0, -- 维生素A (%)
    vitamin_c DECIMAL(8,2) DEFAULT 0, -- 维生素C (%)
    calcium DECIMAL(8,2) DEFAULT 0, -- 钙 (%)
    iron DECIMAL(8,2) DEFAULT 0, -- 铁 (%)

    -- 维生素和矿物质的NRV%
    vitamin_a_nrv DECIMAL(5,2) DEFAULT 0, -- 维生素A NRV% (基于800μg)
    vitamin_c_nrv DECIMAL(5,2) DEFAULT 0, -- 维生素C NRV% (基于100mg)
    calcium_nrv DECIMAL(5,2) DEFAULT 0, -- 钙 NRV% (基于800mg)
    iron_nrv DECIMAL(5,2) DEFAULT 0, -- 铁 NRV% (基于15mg)
    
    -- 扩展营养成分 (JSON格式存储其他营养元素)
    extended_nutrition JSON,
    
    -- 其他信息
    serving_size DECIMAL(8,2) DEFAULT 100, -- 标准份量 (g)
    serving_unit VARCHAR(20) DEFAULT 'g', -- 份量单位
    description TEXT, -- 描述
    image_url VARCHAR(255), -- 食物图片
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_name (name),
    INDEX idx_is_public (is_public),
    INDEX idx_barcode (barcode)
);

-- 4. 食物标签关联表
CREATE TABLE food_tag_relations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    food_id INT NOT NULL,
    tag_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (food_id) REFERENCES foods(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES food_tags(id) ON DELETE CASCADE,
    UNIQUE KEY unique_food_tag (food_id, tag_id),
    INDEX idx_food_id (food_id),
    INDEX idx_tag_id (tag_id)
);

-- 5. 每日营养摄入记录表
CREATE TABLE daily_nutrition_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    record_date DATE NOT NULL,
    meal_type ENUM('breakfast', 'lunch', 'dinner', 'snack') NOT NULL,
    food_id INT NOT NULL,
    quantity DECIMAL(8,2) NOT NULL, -- 摄入量
    unit VARCHAR(20) DEFAULT 'g', -- 单位
    
    -- 实际摄入的营养成分 (根据quantity计算)
    actual_calories DECIMAL(8,2) DEFAULT 0,
    actual_carbohydrates DECIMAL(8,2) DEFAULT 0,
    actual_total_fat DECIMAL(8,2) DEFAULT 0,
    actual_protein DECIMAL(8,2) DEFAULT 0,
    actual_saturated_fat DECIMAL(8,2) DEFAULT 0,
    actual_trans_fat DECIMAL(8,2) DEFAULT 0,
    actual_cholesterol DECIMAL(8,2) DEFAULT 0,
    actual_sodium DECIMAL(8,2) DEFAULT 0,
    actual_potassium DECIMAL(8,2) DEFAULT 0,
    actual_dietary_fiber DECIMAL(8,2) DEFAULT 0,
    actual_sugar DECIMAL(8,2) DEFAULT 0,
    actual_vitamin_a DECIMAL(8,2) DEFAULT 0,
    actual_vitamin_c DECIMAL(8,2) DEFAULT 0,
    actual_calcium DECIMAL(8,2) DEFAULT 0,
    actual_iron DECIMAL(8,2) DEFAULT 0,

    -- 实际摄入的NRV% (根据quantity计算)
    actual_calories_nrv DECIMAL(8,2) DEFAULT 0,
    actual_carbohydrates_nrv DECIMAL(8,2) DEFAULT 0,
    actual_total_fat_nrv DECIMAL(8,2) DEFAULT 0,
    actual_protein_nrv DECIMAL(8,2) DEFAULT 0,
    actual_saturated_fat_nrv DECIMAL(8,2) DEFAULT 0,
    actual_cholesterol_nrv DECIMAL(8,2) DEFAULT 0,
    actual_sodium_nrv DECIMAL(8,2) DEFAULT 0,
    actual_potassium_nrv DECIMAL(8,2) DEFAULT 0,
    actual_dietary_fiber_nrv DECIMAL(8,2) DEFAULT 0,
    actual_vitamin_a_nrv DECIMAL(8,2) DEFAULT 0,
    actual_vitamin_c_nrv DECIMAL(8,2) DEFAULT 0,
    actual_calcium_nrv DECIMAL(8,2) DEFAULT 0,
    actual_iron_nrv DECIMAL(8,2) DEFAULT 0,

    -- 扩展营养成分的实际摄入量
    actual_extended_nutrition JSON,
    
    notes TEXT, -- 备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (food_id) REFERENCES foods(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_date_meal_food (user_id, record_date, meal_type, food_id),
    INDEX idx_user_date (user_id, record_date),
    INDEX idx_meal_type (meal_type)
);

-- 6. 每日营养汇总表 (可选，用于快速查询每日总摄入量)
CREATE TABLE daily_nutrition_summary (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    summary_date DATE NOT NULL,
    
    -- 每日总摄入量
    total_calories DECIMAL(10,2) DEFAULT 0,
    total_carbohydrates DECIMAL(10,2) DEFAULT 0,
    total_fat DECIMAL(10,2) DEFAULT 0,
    total_protein DECIMAL(10,2) DEFAULT 0,
    total_saturated_fat DECIMAL(10,2) DEFAULT 0,
    total_trans_fat DECIMAL(10,2) DEFAULT 0,
    total_cholesterol DECIMAL(10,2) DEFAULT 0,
    total_sodium DECIMAL(10,2) DEFAULT 0,
    total_potassium DECIMAL(10,2) DEFAULT 0,
    total_dietary_fiber DECIMAL(10,2) DEFAULT 0,
    total_sugar DECIMAL(10,2) DEFAULT 0,
    total_vitamin_a DECIMAL(10,2) DEFAULT 0,
    total_vitamin_c DECIMAL(10,2) DEFAULT 0,
    total_calcium DECIMAL(10,2) DEFAULT 0,
    total_iron DECIMAL(10,2) DEFAULT 0,

    -- 每日总NRV%
    total_calories_nrv DECIMAL(10,2) DEFAULT 0,
    total_carbohydrates_nrv DECIMAL(10,2) DEFAULT 0,
    total_fat_nrv DECIMAL(10,2) DEFAULT 0,
    total_protein_nrv DECIMAL(10,2) DEFAULT 0,
    total_saturated_fat_nrv DECIMAL(10,2) DEFAULT 0,
    total_cholesterol_nrv DECIMAL(10,2) DEFAULT 0,
    total_sodium_nrv DECIMAL(10,2) DEFAULT 0,
    total_potassium_nrv DECIMAL(10,2) DEFAULT 0,
    total_dietary_fiber_nrv DECIMAL(10,2) DEFAULT 0,
    total_vitamin_a_nrv DECIMAL(10,2) DEFAULT 0,
    total_vitamin_c_nrv DECIMAL(10,2) DEFAULT 0,
    total_calcium_nrv DECIMAL(10,2) DEFAULT 0,
    total_iron_nrv DECIMAL(10,2) DEFAULT 0,

    -- 扩展营养成分总量
    total_extended_nutrition JSON,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_date (user_id, summary_date),
    INDEX idx_user_id (user_id),
    INDEX idx_summary_date (summary_date)
);

-- 插入系统默认标签
INSERT INTO food_tags (name, color, description, is_system) VALUES
('抗炎', '#52c41a', '具有抗炎作用的食物', TRUE),
('高蛋白', '#1890ff', '蛋白质含量丰富的食物', TRUE),
('低脂', '#faad14', '脂肪含量较低的食物', TRUE),
('高纤维', '#722ed1', '膳食纤维含量丰富的食物', TRUE),
('低糖', '#eb2f96', '糖分含量较低的食物', TRUE),
('有机', '#13c2c2', '有机认证的食物', TRUE),
('素食', '#52c41a', '适合素食者的食物', TRUE),
('无麸质', '#fa8c16', '不含麸质的食物', TRUE),
('超级食物', '#f5222d', '营养价值极高的食物', TRUE),
('发酵食品', '#a0d911', '经过发酵的食物', TRUE);

-- 创建触发器：自动更新每日营养汇总
DELIMITER //

CREATE TRIGGER update_daily_summary_after_insert
AFTER INSERT ON daily_nutrition_records
FOR EACH ROW
BEGIN
    INSERT INTO daily_nutrition_summary (
        user_id, summary_date,
        total_calories, total_carbohydrates, total_fat, total_protein,
        total_saturated_fat, total_trans_fat, total_cholesterol,
        total_sodium, total_potassium, total_dietary_fiber, total_sugar,
        total_vitamin_a, total_vitamin_c, total_calcium, total_iron,
        total_calories_nrv, total_carbohydrates_nrv, total_fat_nrv, total_protein_nrv,
        total_saturated_fat_nrv, total_cholesterol_nrv, total_sodium_nrv,
        total_potassium_nrv, total_dietary_fiber_nrv, total_vitamin_a_nrv,
        total_vitamin_c_nrv, total_calcium_nrv, total_iron_nrv
    ) VALUES (
        NEW.user_id, NEW.record_date,
        NEW.actual_calories, NEW.actual_carbohydrates, NEW.actual_total_fat, NEW.actual_protein,
        NEW.actual_saturated_fat, NEW.actual_trans_fat, NEW.actual_cholesterol,
        NEW.actual_sodium, NEW.actual_potassium, NEW.actual_dietary_fiber, NEW.actual_sugar,
        NEW.actual_vitamin_a, NEW.actual_vitamin_c, NEW.actual_calcium, NEW.actual_iron,
        NEW.actual_calories_nrv, NEW.actual_carbohydrates_nrv, NEW.actual_total_fat_nrv, NEW.actual_protein_nrv,
        NEW.actual_saturated_fat_nrv, NEW.actual_cholesterol_nrv, NEW.actual_sodium_nrv,
        NEW.actual_potassium_nrv, NEW.actual_dietary_fiber_nrv, NEW.actual_vitamin_a_nrv,
        NEW.actual_vitamin_c_nrv, NEW.actual_calcium_nrv, NEW.actual_iron_nrv
    ) ON DUPLICATE KEY UPDATE
        total_calories = total_calories + NEW.actual_calories,
        total_carbohydrates = total_carbohydrates + NEW.actual_carbohydrates,
        total_fat = total_fat + NEW.actual_total_fat,
        total_protein = total_protein + NEW.actual_protein,
        total_saturated_fat = total_saturated_fat + NEW.actual_saturated_fat,
        total_trans_fat = total_trans_fat + NEW.actual_trans_fat,
        total_cholesterol = total_cholesterol + NEW.actual_cholesterol,
        total_sodium = total_sodium + NEW.actual_sodium,
        total_potassium = total_potassium + NEW.actual_potassium,
        total_dietary_fiber = total_dietary_fiber + NEW.actual_dietary_fiber,
        total_sugar = total_sugar + NEW.actual_sugar,
        total_vitamin_a = total_vitamin_a + NEW.actual_vitamin_a,
        total_vitamin_c = total_vitamin_c + NEW.actual_vitamin_c,
        total_calcium = total_calcium + NEW.actual_calcium,
        total_iron = total_iron + NEW.actual_iron,
        total_calories_nrv = total_calories_nrv + NEW.actual_calories_nrv,
        total_carbohydrates_nrv = total_carbohydrates_nrv + NEW.actual_carbohydrates_nrv,
        total_fat_nrv = total_fat_nrv + NEW.actual_total_fat_nrv,
        total_protein_nrv = total_protein_nrv + NEW.actual_protein_nrv,
        total_saturated_fat_nrv = total_saturated_fat_nrv + NEW.actual_saturated_fat_nrv,
        total_cholesterol_nrv = total_cholesterol_nrv + NEW.actual_cholesterol_nrv,
        total_sodium_nrv = total_sodium_nrv + NEW.actual_sodium_nrv,
        total_potassium_nrv = total_potassium_nrv + NEW.actual_potassium_nrv,
        total_dietary_fiber_nrv = total_dietary_fiber_nrv + NEW.actual_dietary_fiber_nrv,
        total_vitamin_a_nrv = total_vitamin_a_nrv + NEW.actual_vitamin_a_nrv,
        total_vitamin_c_nrv = total_vitamin_c_nrv + NEW.actual_vitamin_c_nrv,
        total_calcium_nrv = total_calcium_nrv + NEW.actual_calcium_nrv,
        total_iron_nrv = total_iron_nrv + NEW.actual_iron_nrv,
        updated_at = CURRENT_TIMESTAMP;
END//

CREATE TRIGGER update_daily_summary_after_delete
AFTER DELETE ON daily_nutrition_records
FOR EACH ROW
BEGIN
    UPDATE daily_nutrition_summary SET
        total_calories = total_calories - OLD.actual_calories,
        total_carbohydrates = total_carbohydrates - OLD.actual_carbohydrates,
        total_fat = total_fat - OLD.actual_total_fat,
        total_protein = total_protein - OLD.actual_protein,
        total_saturated_fat = total_saturated_fat - OLD.actual_saturated_fat,
        total_trans_fat = total_trans_fat - OLD.actual_trans_fat,
        total_cholesterol = total_cholesterol - OLD.actual_cholesterol,
        total_sodium = total_sodium - OLD.actual_sodium,
        total_potassium = total_potassium - OLD.actual_potassium,
        total_dietary_fiber = total_dietary_fiber - OLD.actual_dietary_fiber,
        total_sugar = total_sugar - OLD.actual_sugar,
        total_vitamin_a = total_vitamin_a - OLD.actual_vitamin_a,
        total_vitamin_c = total_vitamin_c - OLD.actual_vitamin_c,
        total_calcium = total_calcium - OLD.actual_calcium,
        total_iron = total_iron - OLD.actual_iron,
        total_calories_nrv = total_calories_nrv - OLD.actual_calories_nrv,
        total_carbohydrates_nrv = total_carbohydrates_nrv - OLD.actual_carbohydrates_nrv,
        total_fat_nrv = total_fat_nrv - OLD.actual_total_fat_nrv,
        total_protein_nrv = total_protein_nrv - OLD.actual_protein_nrv,
        total_saturated_fat_nrv = total_saturated_fat_nrv - OLD.actual_saturated_fat_nrv,
        total_cholesterol_nrv = total_cholesterol_nrv - OLD.actual_cholesterol_nrv,
        total_sodium_nrv = total_sodium_nrv - OLD.actual_sodium_nrv,
        total_potassium_nrv = total_potassium_nrv - OLD.actual_potassium_nrv,
        total_dietary_fiber_nrv = total_dietary_fiber_nrv - OLD.actual_dietary_fiber_nrv,
        total_vitamin_a_nrv = total_vitamin_a_nrv - OLD.actual_vitamin_a_nrv,
        total_vitamin_c_nrv = total_vitamin_c_nrv - OLD.actual_vitamin_c_nrv,
        total_calcium_nrv = total_calcium_nrv - OLD.actual_calcium_nrv,
        total_iron_nrv = total_iron_nrv - OLD.actual_iron_nrv,
        updated_at = CURRENT_TIMESTAMP
    WHERE user_id = OLD.user_id AND summary_date = OLD.record_date;
END//

DELIMITER ;

-- 7. 饮食类型表
CREATE TABLE diet_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL, -- 饮食类型名称，如"地中海饮食"、"DASH饮食"等
    name_en VARCHAR(100), -- 英文名称
    description TEXT, -- 饮食类型描述
    nutrition_features TEXT, -- 营养特点
    benefits TEXT, -- 益处
    suitable_people TEXT, -- 适合人群
    image_url VARCHAR(255), -- 饮食类型图片
    color VARCHAR(7) DEFAULT '#1890ff', -- 主题颜色
    is_system BOOLEAN DEFAULT FALSE, -- 是否为系统预设类型
    user_id INT, -- 自定义类型的创建者，系统类型为NULL
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_system (is_system),
    INDEX idx_name (name)
);

-- 8. 饮食类型食物分类表
CREATE TABLE diet_type_food_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    diet_type_id INT NOT NULL,
    category_name VARCHAR(100) NOT NULL, -- 食物分类名称，如"蔬菜"、"水果"、"全谷物"等
    category_name_en VARCHAR(100), -- 英文名称
    description TEXT, -- 分类描述
    examples TEXT, -- 食物示例，如"西红柿、黄瓜、菠菜"
    recommended_amount VARCHAR(100), -- 推荐摄入量，如"每日300-500g"
    sort_order INT DEFAULT 0, -- 排序顺序
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (diet_type_id) REFERENCES diet_types(id) ON DELETE CASCADE,
    INDEX idx_diet_type_id (diet_type_id),
    INDEX idx_sort_order (sort_order)
);

-- 9. 每日饮食计划表
CREATE TABLE daily_diet_plans (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    plan_date DATE NOT NULL,
    diet_type_id INT, -- 选择的饮食类型，可为空
    plan_name VARCHAR(200), -- 计划名称，如"我的地中海饮食计划"
    notes TEXT, -- 备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (diet_type_id) REFERENCES diet_types(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_date (user_id, plan_date),
    INDEX idx_user_id (user_id),
    INDEX idx_plan_date (plan_date),
    INDEX idx_diet_type_id (diet_type_id)
);

-- 10. 每日饮食计划项目表
CREATE TABLE daily_diet_plan_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    daily_diet_plan_id INT NOT NULL,
    category_name VARCHAR(100) NOT NULL, -- 食物分类名称
    category_description TEXT, -- 分类描述
    specific_foods TEXT, -- 具体食物明细，JSON格式存储
    is_consumed BOOLEAN DEFAULT FALSE, -- 是否已摄入
    consumed_at TIMESTAMP NULL, -- 摄入时间
    notes TEXT, -- 备注
    sort_order INT DEFAULT 0, -- 排序顺序
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (daily_diet_plan_id) REFERENCES daily_diet_plans(id) ON DELETE CASCADE,
    INDEX idx_daily_diet_plan_id (daily_diet_plan_id),
    INDEX idx_is_consumed (is_consumed),
    INDEX idx_sort_order (sort_order)
);

-- 插入系统预设饮食类型
INSERT INTO diet_types (name, name_en, description, nutrition_features, benefits, suitable_people, color, is_system) VALUES
('地中海饮食', 'Mediterranean Diet',
 '地中海饮食是一种基于地中海沿岸国家传统饮食模式的健康饮食方式，强调新鲜蔬果、全谷物、豆类、坚果、橄榄油和适量鱼类的摄入。',
 '富含单不饱和脂肪酸、膳食纤维、抗氧化物质；低饱和脂肪；适量蛋白质',
 '降低心血管疾病风险、改善认知功能、抗炎、延缓衰老、有助于体重管理',
 '心血管疾病患者、糖尿病患者、需要减重的人群、中老年人群',
 '#52c41a', TRUE),

('DASH饮食', 'DASH Diet',
 'DASH饮食（Dietary Approaches to Stop Hypertension）是专门为降低血压而设计的饮食模式，强调低钠、高钾、高纤维的食物选择。',
 '低钠高钾、富含膳食纤维、适量蛋白质、低饱和脂肪',
 '降低血压、改善心血管健康、预防中风、有助于体重控制',
 '高血压患者、心血管疾病高危人群、需要控制体重的人群',
 '#1890ff', TRUE),

('植物性饮食', 'Plant-Based Diet',
 '植物性饮食以植物来源的食物为主，包括蔬菜、水果、全谷物、豆类、坚果和种子，限制或避免动物产品。',
 '高膳食纤维、富含植物化学物质、低饱和脂肪、零胆固醇',
 '降低慢性疾病风险、环保可持续、改善消化健康、有助于体重管理',
 '素食主义者、环保主义者、消化系统敏感人群、需要减重的人群',
 '#722ed1', TRUE),

('低碳水化合物饮食', 'Low-Carb Diet',
 '低碳水化合物饮食限制碳水化合物的摄入，增加蛋白质和健康脂肪的比例，促进身体燃烧脂肪获取能量。',
 '低碳水化合物、高蛋白质、适量健康脂肪',
 '快速减重、改善血糖控制、提高饱腹感、可能改善心血管指标',
 '需要快速减重的人群、2型糖尿病患者、代谢综合征患者',
 '#fa8c16', TRUE),

('日式饮食', 'Japanese Diet',
 '日式饮食以米饭为主食，搭配鱼类、蔬菜、豆制品和海藻，注重食材的新鲜和营养平衡。',
 '低脂肪、适量蛋白质、富含omega-3脂肪酸、高纤维',
 '长寿、降低心血管疾病风险、维持健康体重、改善肠道健康',
 '追求长寿的人群、心血管疾病患者、需要控制体重的人群',
 '#13c2c2', TRUE),

('传统中式饮食', 'Traditional Chinese Diet',
 '传统中式饮食注重食物的平衡搭配，强调"药食同源"，以谷物为主，搭配蔬菜、豆类和适量肉类。',
 '营养均衡、食材多样、注重食物性味、季节性搭配',
 '营养全面、易消化、符合中医养生理念、适应性强',
 '中国人群、消化功能较弱的人群、追求传统养生的人群',
 '#eb2f96', TRUE);

-- 插入地中海饮食的食物分类
INSERT INTO diet_type_food_categories (diet_type_id, category_name, category_name_en, description, examples, recommended_amount, sort_order) VALUES
(1, '蔬菜', 'Vegetables', '新鲜的各类蔬菜，富含维生素、矿物质和膳食纤维', '西红柿、黄瓜、菠菜、茄子、洋葱、胡萝卜', '每日400-500g', 1),
(1, '水果', 'Fruits', '新鲜的季节性水果，提供维生素C和抗氧化物质', '橙子、苹果、葡萄、无花果、柠檬', '每日200-300g', 2),
(1, '全谷物', 'Whole Grains', '未精制的谷物，提供复合碳水化合物和B族维生素', '全麦面包、糙米、燕麦、大麦', '每日150-200g', 3),
(1, '豆类', 'Legumes', '各种豆类，富含植物蛋白和膳食纤维', '扁豆、鹰嘴豆、白豆、黑豆', '每周3-4次', 4),
(1, '坚果', 'Nuts', '各种坚果，提供健康脂肪和蛋白质', '核桃、杏仁、榛子、松子', '每日30g', 5),
(1, '橄榄油', 'Olive Oil', '特级初榨橄榄油，主要的脂肪来源', '特级初榨橄榄油', '每日2-3汤匙', 6),
(1, '鱼类', 'Fish', '富含omega-3脂肪酸的鱼类', '三文鱼、沙丁鱼、鲭鱼、金枪鱼', '每周2-3次', 7),
(1, '红酒', 'Red Wine', '适量的红酒（可选）', '干红葡萄酒', '每日1小杯（可选）', 8);

-- 插入DASH饮食的食物分类
INSERT INTO diet_type_food_categories (diet_type_id, category_name, category_name_en, description, examples, recommended_amount, sort_order) VALUES
(2, '蔬菜', 'Vegetables', '各种新鲜蔬菜，特别是深色蔬菜', '菠菜、西兰花、胡萝卜、甜椒', '每日4-5份', 1),
(2, '水果', 'Fruits', '新鲜水果，富含钾和维生素', '香蕉、橙子、苹果、浆果', '每日4-5份', 2),
(2, '全谷物', 'Whole Grains', '全谷物食品，提供膳食纤维', '全麦面包、糙米、燕麦片', '每日6-8份', 3),
(2, '低脂乳制品', 'Low-fat Dairy', '低脂或脱脂乳制品，提供钙质', '脱脂牛奶、低脂酸奶、低脂奶酪', '每日2-3份', 4),
(2, '瘦肉蛋白', 'Lean Protein', '瘦肉、鱼类、蛋类', '鸡胸肉、鱼肉、鸡蛋、豆腐', '每日不超过6份', 5),
(2, '坚果种子', 'Nuts and Seeds', '无盐坚果和种子', '杏仁、核桃、葵花籽', '每周4-5份', 6);

-- 插入植物性饮食的食物分类
INSERT INTO diet_type_food_categories (diet_type_id, category_name, category_name_en, description, examples, recommended_amount, sort_order) VALUES
(3, '绿叶蔬菜', 'Leafy Greens', '各种绿叶蔬菜，营养密度高', '菠菜、羽衣甘蓝、生菜、芝麻菜', '每日至少1份', 1),
(3, '十字花科蔬菜', 'Cruciferous Vegetables', '十字花科蔬菜，含有抗癌物质', '西兰花、花椰菜、卷心菜、萝卜', '每日1-2份', 2),
(3, '浆果类', 'Berries', '各种浆果，富含抗氧化物质', '蓝莓、草莓、覆盆子、黑莓', '每日1份', 3),
(3, '豆类', 'Legumes', '各种豆类，植物蛋白的主要来源', '黑豆、扁豆、鹰嘴豆、豌豆', '每日1-2份', 4),
(3, '全谷物', 'Whole Grains', '未加工的全谷物', '藜麦、糙米、燕麦、全麦', '每日3-4份', 5),
(3, '坚果种子', 'Nuts and Seeds', '各种坚果和种子', '核桃、亚麻籽、奇亚籽、杏仁', '每日1-2份', 6);

-- 插入低碳水化合物饮食的食物分类
INSERT INTO diet_type_food_categories (diet_type_id, category_name, category_name_en, description, examples, recommended_amount, sort_order) VALUES
(4, '蛋白质', 'Protein', '高质量蛋白质来源', '鸡肉、牛肉、鱼肉、鸡蛋', '每餐20-30g', 1),
(4, '健康脂肪', 'Healthy Fats', '单不饱和和多不饱和脂肪', '橄榄油、牛油果、坚果、鱼油', '每日适量', 2),
(4, '低碳蔬菜', 'Low-Carb Vegetables', '碳水化合物含量低的蔬菜', '菠菜、西兰花、芦笋、黄瓜', '每日大量', 3),
(4, '乳制品', 'Dairy', '低碳水化合物的乳制品', '奶酪、希腊酸奶、黄油', '适量', 4);

-- 插入日式饮食的食物分类
INSERT INTO diet_type_food_categories (diet_type_id, category_name, category_name_en, description, examples, recommended_amount, sort_order) VALUES
(5, '米饭', 'Rice', '日式饮食的主食', '白米饭、糙米饭', '每餐1碗', 1),
(5, '鱼类', 'Fish', '新鲜的鱼类，主要蛋白质来源', '三文鱼、鲭鱼、金枪鱼、鲷鱼', '每日1-2份', 2),
(5, '蔬菜', 'Vegetables', '各种蔬菜，常用腌制或煮制', '白萝卜、胡萝卜、白菜、茄子', '每餐多种', 3),
(5, '豆制品', 'Soy Products', '各种豆制品', '豆腐、味噌、纳豆、豆浆', '每日1-2份', 4),
(5, '海藻', 'Seaweed', '各种海藻类食物', '海带、紫菜、裙带菜', '经常食用', 5),
(5, '绿茶', 'Green Tea', '日式绿茶', '煎茶、抹茶', '每日多杯', 6);

-- 插入传统中式饮食的食物分类
INSERT INTO diet_type_food_categories (diet_type_id, category_name, category_name_en, description, examples, recommended_amount, sort_order) VALUES
(6, '谷物', 'Grains', '以米饭、面条为主的谷物', '米饭、面条、馒头、粥', '每餐1-2份', 1),
(6, '蔬菜', 'Vegetables', '各种时令蔬菜', '白菜、萝卜、豆角、茄子', '每餐2-3种', 2),
(6, '豆类', 'Legumes', '各种豆类及豆制品', '豆腐、豆浆、红豆、绿豆', '每日1份', 3),
(6, '肉类', 'Meat', '适量的肉类', '猪肉、鸡肉、鱼肉', '每日适量', 4),
(6, '药食同源食材', 'Medicinal Foods', '具有保健功效的食材', '枸杞、红枣、山药、莲子', '经常食用', 5),
(6, '茶', 'Tea', '各种中式茶饮', '绿茶、乌龙茶、普洱茶', '每日适量', 6);
