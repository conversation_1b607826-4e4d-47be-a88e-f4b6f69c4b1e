# 日历组件改进说明

## 🎯 问题修复

### 1. 横线问题
**问题**：日历头部（九月2025）下方有一条横线横穿整个日历
**原因**：V-Calendar 头部样式中的 `border-bottom: 1px solid #f0f0f0;`
**解决方案**：移除了 `:deep(.vc-header)` 中的 `border-bottom` 属性

### 2. 间距调整
**要求**：
- 日期左右和边框直接间隔为4px
- 日期详情模态框之间的间距为10px

**实现**：
```css
:deep(.vc-header) {
  padding: 16px 4px;        /* 左右间距4px */
  margin-bottom: 4px;       /* 头部下方间距4px */
}

:deep(.vc-weeks) {
  padding: 0 4px 10px 4px;  /* 左右间距4px，底部10px */
  margin-top: 0;
}
```

**模态框间距**：
```html
<NModal style="margin-top: 10px;">
```

## 🌙 农历和节气功能

### 1. 依赖安装
```bash
pnpm add lunar-javascript
```

### 2. 功能实现

#### 农历信息显示
- **农历日期**：显示农历日期（如：初一、十五）
- **节气**：显示二十四节气（如：立春、春分）
- **节日**：显示传统节日和公历节日

#### 自定义日期内容
```vue
<template #day-content="{ day }">
  <div class="custom-day-content">
    <div class="solar-date">{{ day.day }}</div>
    <div class="lunar-info">
      <div class="lunar-date">{{ getLunarDay(day.date) }}</div>
      <div v-if="getSolarTerm(day.date)" class="solar-term">
        {{ getSolarTerm(day.date) }}
      </div>
      <div v-if="getFestival(day.date)" class="festival">
        {{ getFestival(day.date) }}
      </div>
    </div>
  </div>
</template>
```

#### 核心方法
```typescript
// 获取农历日期
const getLunarDay = (date: Date): string => {
  const solar = Solar.fromDate(date)
  const lunar = solar.getLunar()
  return lunar.getDayInChinese()
}

// 获取节气
const getSolarTerm = (date: Date): string => {
  const solar = Solar.fromDate(date)
  const lunar = solar.getLunar()
  return lunar.getJieQi() || lunar.getQi() || ''
}

// 获取节日
const getFestival = (date: Date): string => {
  const solar = Solar.fromDate(date)
  const lunar = solar.getLunar()
  const lunarFestivals = lunar.getFestivals()
  const solarFestivals = solar.getFestivals()
  return [...lunarFestivals, ...solarFestivals][0] || ''
}
```

## 🎨 样式优化

### 1. 自定义日期样式
```css
.custom-day-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: 40px;
  padding: 2px;
}

.solar-date {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.lunar-date {
  font-size: 10px;
  color: #666;
}

.solar-term {
  font-size: 9px;
  color: #52c41a;  /* 绿色显示节气 */
  font-weight: 500;
}

.festival {
  font-size: 9px;
  color: #f5222d;  /* 红色显示节日 */
  font-weight: 500;
}
```

### 2. 交互状态
- **今日高亮**：今天的日期背景为蓝色，文字为白色
- **悬停效果**：鼠标悬停时显示淡蓝色背景
- **选中状态**：选中的日期背景为蓝色，所有文字为白色

## 🔧 技术细节

### 1. TypeScript 支持
创建了类型声明文件 `src/types/lunar-javascript.d.ts`：
```typescript
declare module 'lunar-javascript' {
  export class Solar {
    static fromDate(date: Date): Solar;
    getLunar(): Lunar;
    getFestivals(): string[];
  }

  export class Lunar {
    getDayInChinese(): string;
    getMonthInChinese(): string;
    getJieQi(): string | null;
    getQi(): string | null;
    getFestivals(): string[];
  }
}
```

### 2. 错误处理
所有农历相关方法都包含 try-catch 错误处理，确保在农历库出错时不影响日历正常显示。

### 3. 性能优化
- 只为当前月份的日期计算农历信息
- 使用计算属性缓存农历数据
- 避免重复计算

## 📱 显示效果

### 日期显示格式
```
┌─────────┐
│   15    │  ← 公历日期（大字体）
│  十五   │  ← 农历日期（小字体，灰色）
│  立春   │  ← 节气（小字体，绿色）
│ 春节    │  ← 节日（小字体，红色）
└─────────┘
```

### 颜色编码
- **公历日期**：黑色 (#262626)
- **农历日期**：灰色 (#666)
- **节气**：绿色 (#52c41a)
- **节日**：红色 (#f5222d)
- **今日/选中**：白色文字，蓝色背景

## 🚀 使用方法

1. **查看农历**：每个日期下方显示对应的农历日期
2. **查看节气**：在节气当天会显示节气名称
3. **查看节日**：在节日当天会显示节日名称
4. **交互操作**：点击日期查看详细计划，农历信息不影响原有功能

## 📝 注意事项

1. **农历库**：使用 `lunar-javascript` 库，支持完整的农历、节气、节日计算
2. **兼容性**：保持与原有饮食计划功能的完全兼容
3. **性能**：农历计算仅在需要时进行，不影响日历加载速度
4. **错误处理**：农历功能异常时不会影响日历基本功能

这些改进让日历组件更加实用和美观，同时保持了原有的饮食计划管理功能。
