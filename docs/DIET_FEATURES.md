# 饮食类型和每日饮食跟踪功能文档

## 功能概述

本文档介绍了营养追踪应用中新增的饮食类型管理和每日饮食跟踪功能。这些功能允许用户：

1. 管理各种饮食类型（如地中海饮食、DASH饮食等）
2. 创建和管理每日饮食计划
3. 跟踪每日食物摄入情况

## 功能特性

### 🍽️ 饮食类型管理

#### 系统预设饮食类型
应用预设了6种主流饮食类型：

1. **地中海饮食** - 富含橄榄油、鱼类、蔬果的健康饮食
2. **DASH饮食** - 专为降血压设计的饮食模式
3. **植物性饮食** - 以植物来源食物为主的饮食方式
4. **低碳水化合物饮食** - 限制碳水化合物摄入的饮食
5. **日式饮食** - 以米饭、鱼类、蔬菜为主的传统日式饮食
6. **传统中式饮食** - 注重平衡搭配的中式饮食

#### 功能特点
- 📋 查看饮食类型详细信息（营养特点、益处、适合人群）
- ➕ 添加自定义饮食类型
- ✏️ 编辑现有饮食类型（仅限自定义类型）
- 🗑️ 删除自定义饮食类型
- 🏷️ 查看每种饮食类型包含的食物分类

### 📅 每日饮食计划

#### 创建计划
- 选择日期创建饮食计划
- 选择饮食类型（可选）
- 设置计划名称和备注
- 自动创建对应的食物分类

#### 管理计划
- ✏️ 编辑计划信息
- ➕ 添加自定义食物分类
- 🍎 为每个分类添加具体食物
- 📝 设置食物分量和单位

#### 智能建议
- 根据选择的饮食类型自动创建食物分类
- 为不同分类提供常见食物建议
- 智能推荐分量和单位

### 📊 每日饮食跟踪

#### 跟踪功能
- 查看当日饮食计划
- ✅ 对已摄入的食物分类进行打勾
- 📈 实时显示完成进度
- 🔍 筛选查看已摄入/待摄入的食物

#### 进度统计
- 完成度百分比显示
- 已摄入/总计数量统计
- 摄入时间记录
- 完成后的祝贺提示

## 页面导航

### 桌面端
- **饮食类型** - 侧边栏菜单
- **饮食计划** - 侧边栏菜单
- **饮食跟踪** - 侧边栏菜单

### 移动端
- **饮食跟踪** - 底部导航（主要入口）
- **饮食计划** - 底部导航
- **饮食类型** - 侧边抽屉菜单

## 数据结构

### 饮食类型表 (diet_types)
```sql
- id: 主键
- name: 饮食类型名称
- name_en: 英文名称
- description: 描述
- nutrition_features: 营养特点
- benefits: 益处
- suitable_people: 适合人群
- color: 主题颜色
- is_system: 是否为系统预设
```

### 饮食类型食物分类表 (diet_type_food_categories)
```sql
- id: 主键
- diet_type_id: 饮食类型ID
- category_name: 分类名称
- description: 分类描述
- examples: 食物示例
- recommended_amount: 推荐摄入量
- sort_order: 排序
```

### 每日饮食计划表 (daily_diet_plans)
```sql
- id: 主键
- user_id: 用户ID
- plan_date: 计划日期
- diet_type_id: 饮食类型ID（可选）
- plan_name: 计划名称
- notes: 备注
```

### 每日饮食计划项目表 (daily_diet_plan_items)
```sql
- id: 主键
- daily_diet_plan_id: 计划ID
- category_name: 分类名称
- category_description: 分类描述
- specific_foods: 具体食物（JSON）
- is_consumed: 是否已摄入
- consumed_at: 摄入时间
- sort_order: 排序
```

## 使用流程

### 1. 创建饮食计划
1. 访问"饮食计划"页面
2. 点击"创建计划"按钮
3. 选择日期和饮食类型
4. 设置计划名称和备注
5. 选择是否自动创建食物分类
6. 保存计划

### 2. 完善计划内容
1. 为每个食物分类添加具体食物
2. 设置食物分量和单位
3. 添加自定义分类（如需要）

### 3. 每日跟踪
1. 访问"饮食跟踪"页面
2. 查看当日计划
3. 对已摄入的食物分类打勾
4. 查看完成进度

## 技术实现

### 前端技术栈
- Vue 3 + TypeScript
- Naive UI 组件库
- Pinia 状态管理
- Vue Router 路由管理

### 状态管理
- `useDietTypeStore` - 饮食类型管理
- `useDailyDietPlanStore` - 每日饮食计划管理

### 响应式设计
- 桌面端：侧边栏导航 + 网格布局
- 移动端：底部导航 + 单列布局
- 自适应卡片和表单设计

## 开发说明

### 组件结构
```
src/
├── views/
│   ├── DietTypes.vue           # 饮食类型管理页面
│   ├── DailyDietPlan.vue       # 每日饮食计划页面
│   └── DailyDietTracker.vue    # 每日饮食跟踪页面
├── components/
│   ├── DietTypeForm.vue        # 饮食类型表单
│   ├── DietTypeDetail.vue      # 饮食类型详情
│   ├── FoodCategoriesList.vue  # 食物分类列表
│   ├── CreateDietPlanForm.vue  # 创建计划表单
│   ├── AddSpecificFoodForm.vue # 添加食物表单
│   └── AddCategoryForm.vue     # 添加分类表单
└── stores/
    ├── dietType.ts             # 饮食类型状态管理
    └── dailyDietPlan.ts        # 每日计划状态管理
```

### 测试
- 开发环境自动运行功能测试
- 控制台输出测试结果
- 验证数据加载和功能完整性

## 未来扩展

### 计划中的功能
1. 营养成分分析
2. 饮食建议和提醒
3. 数据导出和分享
4. 社区功能和食谱分享
5. 与现有营养记录的集成

### 优化方向
1. 性能优化和缓存策略
2. 离线功能支持
3. 更丰富的数据可视化
4. AI驱动的个性化建议

---

*最后更新：2024年12月*
