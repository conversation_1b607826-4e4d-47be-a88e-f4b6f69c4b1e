# 营养追踪器 API 接口设计

## 基础信息

- 基础URL: `/api/v1`
- 认证方式: JWT Token
- 数据格式: JSON
- 字符编码: UTF-8

## 通用响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 1. 用户管理 API

### 1.1 用户注册
- **POST** `/auth/register`
- **请求体**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string"
}
```

### 1.2 用户登录
- **POST** `/auth/login`
- **请求体**:
```json
{
  "email": "string",
  "password": "string"
}
```

### 1.3 获取用户信息
- **GET** `/users/profile`
- **Headers**: `Authorization: Bearer {token}`

### 1.4 更新用户信息
- **PUT** `/users/profile`
- **Headers**: `Authorization: Bearer {token}`
- **请求体**:
```json
{
  "nickname": "string",
  "gender": "male|female|other",
  "birth_date": "YYYY-MM-DD",
  "height": 175.5,
  "weight": 70.0,
  "activity_level": "sedentary|light|moderate|active|very_active"
}
```

## 2. 食物管理 API

### 2.1 获取食物列表
- **GET** `/foods`
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `limit`: 每页数量 (默认: 20)
  - `search`: 搜索关键词
  - `tags`: 标签ID列表 (逗号分隔)
  - `is_public`: 是否包含公共食物 (true/false)

### 2.2 获取食物详情
- **GET** `/foods/{id}`

### 2.3 创建食物
- **POST** `/foods`
- **Headers**: `Authorization: Bearer {token}`
- **请求体**:
```json
{
  "name": "string",
  "brand": "string",
  "barcode": "string",
  "is_public": false,
  "calories": 100.0,
  "carbohydrates": 25.0,
  "total_fat": 5.0,
  "protein": 10.0,
  "saturated_fat": 2.0,
  "trans_fat": 0.0,
  "cholesterol": 0.0,
  "sodium": 100.0,
  "potassium": 200.0,
  "dietary_fiber": 3.0,
  "sugar": 5.0,
  "vitamin_a": 10.0,
  "vitamin_c": 15.0,
  "calcium": 8.0,
  "iron": 5.0,
  "extended_nutrition": {},
  "serving_size": 100.0,
  "serving_unit": "g",
  "description": "string",
  "tag_ids": [1, 2, 3]
}
```

### 2.4 更新食物
- **PUT** `/foods/{id}`
- **Headers**: `Authorization: Bearer {token}`

### 2.5 删除食物
- **DELETE** `/foods/{id}`
- **Headers**: `Authorization: Bearer {token}`

## 3. 标签管理 API

### 3.1 获取标签列表
- **GET** `/tags`
- **查询参数**:
  - `is_system`: 是否只获取系统标签 (true/false)

### 3.2 创建标签
- **POST** `/tags`
- **Headers**: `Authorization: Bearer {token}`
- **请求体**:
```json
{
  "name": "string",
  "color": "#1890ff",
  "description": "string"
}
```

### 3.3 更新标签
- **PUT** `/tags/{id}`
- **Headers**: `Authorization: Bearer {token}`

### 3.4 删除标签
- **DELETE** `/tags/{id}`
- **Headers**: `Authorization: Bearer {token}`

## 4. 营养记录 API

### 4.1 获取营养记录
- **GET** `/nutrition/records`
- **查询参数**:
  - `page`: 页码
  - `limit`: 每页数量
  - `start_date`: 开始日期 (YYYY-MM-DD)
  - `end_date`: 结束日期 (YYYY-MM-DD)
  - `meal_type`: 餐次类型

### 4.2 创建营养记录
- **POST** `/nutrition/records`
- **Headers**: `Authorization: Bearer {token}`
- **请求体**:
```json
{
  "record_date": "YYYY-MM-DD",
  "meal_type": "breakfast|lunch|dinner|snack",
  "food_id": 1,
  "quantity": 150.0,
  "unit": "g",
  "notes": "string"
}
```

### 4.3 更新营养记录
- **PUT** `/nutrition/records/{id}`
- **Headers**: `Authorization: Bearer {token}`

### 4.4 删除营养记录
- **DELETE** `/nutrition/records/{id}`
- **Headers**: `Authorization: Bearer {token}`

### 4.5 获取每日营养汇总
- **GET** `/nutrition/summary`
- **查询参数**:
  - `date`: 日期 (YYYY-MM-DD)
  - `start_date`: 开始日期
  - `end_date`: 结束日期

### 4.6 获取营养统计
- **GET** `/nutrition/stats`
- **查询参数**:
  - `period`: 统计周期 (week|month|year)
  - `start_date`: 开始日期
  - `end_date`: 结束日期

## 5. 错误码定义

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

## 6. 数据验证规则

### 用户数据
- username: 3-50字符，字母数字下划线
- email: 有效邮箱格式
- password: 6-128字符
- height: 100-250 (cm)
- weight: 30-200 (kg)

### 食物数据
- name: 1-100字符，必填
- brand: 0-100字符
- 营养成分: 非负数
- serving_size: 大于0

### 营养记录
- quantity: 大于0
- record_date: 有效日期格式
- meal_type: 枚举值

## 7. 性能要求

- 响应时间: < 200ms (95%)
- 并发支持: 1000+ QPS
- 数据库连接池: 10-50
- 缓存策略: Redis 缓存热点数据

## 8. 安全要求

- JWT Token 过期时间: 24小时
- 密码加密: bcrypt
- API 限流: 100次/分钟/用户
- 输入验证: 所有用户输入必须验证
- SQL 注入防护: 使用参数化查询
