# NRV% (营养素参考值百分比) 功能说明

## 概述

NRV%（Nutrient Reference Value Percentage）是营养素参考值百分比，表示食物中某种营养素含量占成人每日推荐摄入量的百分比。这个功能帮助用户更直观地了解食物的营养价值。

## 功能特性

### 1. 数据库结构更新

#### 新增字段
- `foods` 表新增 NRV% 字段：
  - `calories_nrv` - 能量NRV%
  - `carbohydrates_nrv` - 碳水化合物NRV%
  - `total_fat_nrv` - 脂肪NRV%
  - `protein_nrv` - 蛋白质NRV%
  - `saturated_fat_nrv` - 饱和脂肪NRV%
  - `cholesterol_nrv` - 胆固醇NRV%
  - `sodium_nrv` - 钠NRV%
  - `potassium_nrv` - 钾NRV%
  - `dietary_fiber_nrv` - 膳食纤维NRV%

#### 数据库迁移
执行 `database/migrations/add_nrv_fields.sql` 来更新现有数据库结构。

### 2. 前端界面优化

#### 添加食物页面 (AddFood.vue)
- **双输入框设计**：每个营养成分都有含量和NRV%两个输入框
- **移动端优化**：移动端垂直排列，桌面端水平排列
- **自动计算功能**：点击"自动计算NRV%"按钮自动计算所有NRV%
- **参考值提示**：每个字段显示对应的NRV参考值

#### 界面布局
```
营养成分 (每100g)  [自动计算NRV%] [显示更多]

热量 (基于2000千卡)
[含量输入框] 千卡  [NRV%输入框] %NRV

碳水化合物 (基于300g)
[含量输入框] 克    [NRV%输入框] %NRV

维生素A (基于800μg)
[含量输入框] μg    [NRV%输入框] %NRV

反式脂肪
[含量输入框] 克    [无NRV参考值]
```

#### 布局特点
- **桌面端**：含量和NRV%输入框水平排列，比例为3:2，充分利用空间
- **移动端**：含量和NRV%输入框保持在同一行，比例为2:1.5，优化触摸体验
- **智能提示**：显示每个营养素的NRV参考值
- **占位符提示**：对于没有NRV参考值的营养素显示"无NRV参考值"
- **统一布局**：所有营养成分使用相同的输入框布局模式

#### 扩展营养成分布局
- **桌面端**：名称(2) + 输入组(5) + 操作(auto)
- **移动端**：垂直排列，名称和输入组各占一行
- **输入组内部**：含量(3) + 单位(1) + NRV%(2)

### 3. NRV参考值标准

基于中国营养学会推荐的成人每日营养素参考摄入量：

| 营养素 | 参考值 | 单位 |
|--------|--------|------|
| 能量 | 2000 | 千卡 |
| 碳水化合物 | 300 | 克 |
| 脂肪 | 60 | 克 |
| 蛋白质 | 60 | 克 |
| 饱和脂肪 | 20 | 克 |
| 胆固醇 | 300 | 毫克 |
| 钠 | 2000 | 毫克 |
| 钾 | 3500 | 毫克 |
| 膳食纤维 | 25 | 克 |
| 维生素A | 800 | 微克 |
| 维生素C | 100 | 毫克 |
| 钙 | 800 | 毫克 |
| 铁 | 15 | 毫克 |

### 4. 扩展营养成分JSON结构

#### 新的JSON格式
```json
[
  {
    "name": "维生素B1",
    "nutrition": 0.12,
    "unit": "mg",
    "nrv": 10.0
  },
  {
    "name": "镁",
    "nutrition": 25.0,
    "unit": "mg",
    "nrv": 6.25
  }
]
```

#### 字段说明
- `name`: 营养成分名称
- `nutrition`: 营养成分含量（数值）
- `unit`: 单位（如mg、μg、g等）
- `nrv`: NRV百分比（如果有参考值的话）

### 5. 计算工具 (nrvCalculator.ts)

#### 主要功能
- `calculateNRV()` - 计算单个营养素的NRV%
- `calculateAllNRV()` - 批量计算所有营养素的NRV%
- `calculateNutrientFromNRV()` - 根据NRV%反推营养素含量
- `validateNRV()` - 验证NRV%值的合理性
- `formatNRV()` - 格式化NRV%显示

#### 使用示例
```typescript
import { calculateNRV } from '@/utils/nrvCalculator'

// 计算100克鸡胸肉的蛋白质NRV%
const proteinNRV = calculateNRV(31, 'protein') // 返回 51.67%
```

## 使用指南

### 1. 添加食物时录入NRV%

1. 在"添加食物"页面填写营养成分含量
2. 点击"自动计算NRV%"按钮自动计算所有NRV%
3. 或者手动在NRV%输入框中填写数值
4. 系统会自动验证数值的合理性

### 2. 移动端使用

- 默认只显示4个基础营养成分（热量、碳水、脂肪、蛋白质）
- 点击"显示更多"展开详细营养成分
- 每个营养成分垂直排列，便于输入

### 3. 桌面端使用

- 显示完整的营养成分列表
- 每行显示2个营养成分
- 含量和NRV%水平排列

## 技术实现

### 1. 响应式设计
```vue
<NSpace :vertical="isMobile" :size="8">
  <NInputNumber v-model:value="formData.calories" />
  <NInputNumber v-model:value="formData.calories_nrv" />
</NSpace>
```

### 2. 自动计算逻辑
```typescript
const autoCalculateAllNRV = () => {
  const nutrients = [
    { key: 'calories', value: formData.calories },
    // ... 其他营养素
  ]
  
  nutrients.forEach(({ key, value }) => {
    if (value > 0) {
      calculateNRVForNutrient(key, value)
    }
  })
}
```

### 3. 数据验证
- NRV%范围：0-999%
- 特殊营养素警告（如钠含量过高）
- 数值合理性检查

## 后续优化建议

1. **个性化NRV参考值**：根据用户年龄、性别、活动水平调整参考值
2. **营养素搭配建议**：基于NRV%提供营养搭配建议
3. **每日摄入量追踪**：在仪表盘中显示每日NRV%摄入情况
4. **营养素缺乏提醒**：当某些营养素摄入不足时提醒用户
5. **批量导入功能**：支持从营养标签图片自动识别并填入数据

## 注意事项

1. NRV%是基于成人平均需求量计算的参考值
2. 实际营养需求因个体差异而有所不同
3. 建议结合专业营养师建议使用
4. 数据仅供参考，不能替代专业医疗建议
