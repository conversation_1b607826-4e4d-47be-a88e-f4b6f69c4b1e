# 饮食计划日历功能

## 功能概述

基于 V-Calendar 实现的饮食计划日历组件，提供直观的日历视图来展示和管理饮食计划的完成情况。用户可以通过不同的颜色标识来快速了解每天的饮食计划状态，并支持点击日期查看详细信息。

## 主要特性

### 1. 日历视图
- **月度视图**：显示整个月的饮食计划概览
- **颜色编码**：
  - 🟢 绿色：已完成（100%完成度）
  - 🟡 橙色：部分完成（1-99%完成度）
  - 🔵 蓝色：已计划但未开始（0%完成度）
  - ⚪ 灰色：无计划
- **进度指示**：每个日期显示小圆点和进度条

### 2. 时间线视图
- **时间轴布局**：按时间顺序显示所有饮食计划
- **完成度统计**：显示每个计划的完成进度
- **快速操作**：支持查看详情和开始跟踪

### 3. 统计面板
- **总计划数**：显示创建的饮食计划总数
- **已完成数**：显示完全完成的计划数量
- **平均完成度**：所有计划的平均完成百分比
- **连续天数**：连续完成计划的天数统计

### 4. 交互功能
- **日期点击**：点击日历中的日期查看当天详细计划
- **实时更新**：在弹窗中直接勾选完成状态
- **快速创建**：对于没有计划的日期，可以快速跳转创建
- **导航跳转**：支持跳转到计划管理和跟踪页面

## 技术实现

### 依赖库
- **V-Calendar 3.x**：Vue 3 兼容的日历组件库
- **Naive UI**：UI 组件库
- **Vue Router**：路由管理

### 组件结构
```
src/
├── components/
│   └── DietPlanCalendar.vue      # 核心日历组件
├── views/
│   └── DietPlanCalendarView.vue  # 日历页面视图
└── stores/
    └── dailyDietPlan.ts          # 数据状态管理（已扩展）
```

### 核心功能

#### 1. 日历属性生成
```typescript
const calendarAttributes = computed(() => {
  const attributes: any[] = []
  
  dailyPlanStore.dailyPlans.forEach(plan => {
    const completionRate = getCompletionRate(plan.id)
    let dotColor = getColorByCompletion(completionRate)
    
    attributes.push({
      key: `plan-${plan.id}`,
      dates: new Date(plan.planDate),
      dot: { color: dotColor },
      bar: { color: dotColor },
      popover: { label: `${plan.planName} (${completionRate}%)` }
    })
  })
  
  return attributes
})
```

#### 2. 完成度计算
```typescript
const getCompletionRate = (planId: number): number => {
  const items = dailyPlanStore.getPlanItemsByPlanId(planId)
  if (items.length === 0) return 0
  
  const completedItems = items.filter(item => item.isConsumed).length
  return Math.round((completedItems / items.length) * 100)
}
```

#### 3. 模拟数据生成
- 自动生成最近30天的饮食计划数据
- 随机分配饮食类型和完成状态
- 确保数据的多样性和真实性

## 使用方法

### 1. 访问日历页面
在应用导航中点击"饮食日历"菜单项，或直接访问 `/diet-calendar` 路径。

### 2. 查看计划概览
- 在日历视图中，不同颜色的标识表示不同的完成状态
- 鼠标悬停可以看到计划名称和完成度
- 统计卡片显示整体的计划情况

### 3. 查看详细信息
- 点击任意日期打开详情弹窗
- 在弹窗中可以查看当天的具体计划项目
- 支持直接勾选完成状态

### 4. 切换视图模式
- 点击"日历视图"按钮查看月度日历
- 点击"时间线视图"按钮查看时间轴布局
- 两种视图提供不同的信息展示方式

### 5. 快速操作
- 点击"创建计划"跳转到计划创建页面
- 在时间线视图中点击"开始跟踪"跳转到跟踪页面
- 对于无计划的日期，可以快速创建新计划

## 样式定制

### 日历样式
```css
/* V-Calendar 自定义样式 */
:deep(.vc-container) {
  border: none;
  border-radius: 8px;
}

:deep(.diet-plan-dot) {
  width: 6px;
  height: 6px;
}

:deep(.diet-plan-bar) {
  height: 3px;
  border-radius: 2px;
}
```

### 颜色主题
- 完成：`#52c41a` (绿色)
- 部分完成：`#faad14` (橙色)  
- 已计划：`#1890ff` (蓝色)
- 无计划：`#d9d9d9` (灰色)

## 响应式设计

- **桌面端**：完整的双列布局，统计卡片网格显示
- **移动端**：单列布局，统计卡片2x2网格，时间线垂直排列
- **自适应**：根据屏幕尺寸自动调整组件大小和布局

## 扩展建议

### 1. 数据持久化
- 集成真实的后端API
- 实现数据的增删改查操作
- 添加数据同步和缓存机制

### 2. 高级功能
- 添加周视图和年视图
- 支持计划模板和批量创建
- 实现计划分享和导出功能

### 3. 用户体验优化
- 添加加载状态和错误处理
- 实现拖拽操作移动计划
- 添加键盘快捷键支持

### 4. 数据分析
- 添加更多统计图表
- 实现趋势分析和预测
- 提供个性化建议

## 注意事项

1. **性能优化**：大量数据时考虑虚拟滚动和分页加载
2. **浏览器兼容**：V-Calendar 需要现代浏览器支持
3. **国际化**：目前仅支持中文，可扩展多语言支持
4. **数据验证**：确保日期格式和数据完整性
